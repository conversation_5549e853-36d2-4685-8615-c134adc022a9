'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { cn, Popover, PopoverContent, PopoverTrigger } from '@learn-platform/shared-ui';
import { MultiStepExplain } from '../templates';
import { api } from '../../trpc';
import {
  Clock,
  Trophy,
  FileQuestion,
  Eye,
  RotateCcw,
  MoreVertical,
  Loader2,
  Bookmark,
  StickyNote,
  Check
} from 'lucide-react';
import type { StepConfig } from '../templates/types';
import type { QuizHistoryItem } from '../quiz/types';

interface LearningContentDisplayProps {
  steps: StepConfig[];
  initialStep?: number;
  onStepChange?: (step: number, isLessonComplete?: boolean) => void;
  contentId: string;
  onQuizSelect?: (quizId: string, attemptId?: string) => void;
  onRetakeQuiz?: (quizId: string) => void;
  onGenerateNew?: () => void;
  className?: string;
  totalTimeSpent?: number;
  sessionCount?: number;
}

export function LearningContentDisplay({
  steps,
  initialStep,
  onStepChange,
  contentId,
  onQuizSelect,
  onRetakeQuiz,
  onGenerateNew,
  className,
  totalTimeSpent = 0,
  sessionCount = 0
}: LearningContentDisplayProps) {
  const [currentStep, setCurrentStep] = useState(initialStep ?? 0);
  const [timeSpent, setTimeSpent] = useState(0);
  const [stepStartTime, setStepStartTime] = useState<number>(Date.now());
  const [isLessonCompleted, setIsLessonCompleted] = useState(false);

  // State for note dialog
  const [showNoteDialog, setShowNoteDialog] = useState(false);
  const [noteContent, setNoteContent] = useState('');
  const [editingNoteIndex, setEditingNoteIndex] = useState<number | null>(null);

  // Trophy animation state
  const [trophyAnimations, setTrophyAnimations] = useState<Set<number>>(new Set());
  const previousCompletedStepsRef = useRef<number[]>([]);

  // Fetch learning progress
  const { data: progressData } = api.learningProgress.getProgress.useQuery({ contentId });

  // State for pagination
  const [quizHistoryOffset, setQuizHistoryOffset] = useState(0);
  const [allQuizAttempts, setAllQuizAttempts] = useState<QuizHistoryItem[]>([]);
  const [hasMoreQuizzes, setHasMoreQuizzes] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Fetch quiz history
  const { data: quizHistoryData } = api.quiz.getHistoryByLearningContent.useQuery({
    learningContentId: contentId,
    limit: 3,
    offset: 0, // Always start with 0 for initial load
  }, {
    enabled: true,
    refetchOnWindowFocus: false,
  });

  // Get tRPC utils for cache invalidation
  const utils = api.useUtils();

  // Bookmark mutations
  const addBookmarkMutation = api.learningProgress.addBookmark.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
    },
  });

  const removeBookmarkMutation = api.learningProgress.removeBookmark.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
    },
  });

  // Note mutations
  const addNoteMutation = api.learningProgress.addNote.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
      setShowNoteDialog(false);
      setNoteContent('');
      setEditingNoteIndex(null);
    },
  });

  const updateNoteMutation = api.learningProgress.updateNote.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getMy.invalidate();
      setShowNoteDialog(false);
      setNoteContent('');
      setEditingNoteIndex(null);
    },
  });

  const deleteNoteMutation = api.learningProgress.deleteNote.useMutation({
    onSuccess: () => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getAll.invalidate();
    },
  });

  // Progress update mutation
  const updateProgressMutation = api.learningProgress.updateProgress.useMutation({
    onSuccess: (data, variables) => {
      utils.learningProgress.getProgress.invalidate({ contentId });
      utils.learningProgress.getLatestInProgress.invalidate();
      utils.learningContent.getAll.invalidate();

      // Trigger trophy animation only for newly completed steps after successful save
      if (variables.completedSteps) {
        const previousCompleted = previousCompletedStepsRef.current;
        const newlyCompleted = variables.completedSteps.filter(step => !previousCompleted.includes(step));

        // Animate only the newly completed steps (not all completed steps)
        newlyCompleted.forEach(stepIndex => {
          triggerTrophyAnimation(stepIndex);
        });

        // Update the ref to track the new completed steps
        previousCompletedStepsRef.current = [...variables.completedSteps];
      }
    },
  });

  // Initialize previousCompletedStepsRef with existing progress data to prevent animating all existing completed steps
  useEffect(() => {
    if (progressData?.progress?.completedSteps) {
      previousCompletedStepsRef.current = [...progressData.progress.completedSteps];
    }
  }, [progressData?.progress?.completedSteps]);

  // Update quiz attempts when new data arrives
  useEffect(() => {
    if (quizHistoryData) {
      if (quizHistoryOffset === 0) {
        // First load - replace all attempts
        setAllQuizAttempts(quizHistoryData.attempts as QuizHistoryItem[] || []);
      } else {
        // Subsequent loads - append new attempts
        setAllQuizAttempts(prev => [...prev, ...(quizHistoryData.attempts as QuizHistoryItem[] || [])]);
      }
      setHasMoreQuizzes(quizHistoryData.hasMore || false);
      setIsLoadingMore(false);
    }
  }, [quizHistoryData]);

  // Load more quiz attempts
  const handleLoadMore = async () => {
    if (isLoadingMore || !hasMoreQuizzes) return;

    setIsLoadingMore(true);
    const newOffset = quizHistoryOffset + 3;

    try {
      // Manually fetch more data without triggering useQuery re-render
      const response = await utils.quiz.getHistoryByLearningContent.fetch({
        learningContentId: contentId,
        limit: 3,
        offset: newOffset,
      });

      if (response) {
        setAllQuizAttempts(prev => [...prev, ...(response.attempts as QuizHistoryItem[] || [])]);
        setHasMoreQuizzes(response.hasMore || false);
        setQuizHistoryOffset(newOffset);
      }
    } catch (error) {
      console.error('Failed to load more quiz attempts:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const quizHistory = allQuizAttempts;
  const stats = quizHistoryData?.stats || {
    totalAttempts: 0,
    completedAttempts: 0,
    averageScore: 0,
    bestScore: 0,
  };

  // Derived state with memoization
  const progress = progressData?.progress;

  // Track time spent with optimized interval
  useEffect(() => {
    // Don't track time in review mode or if lesson is already completed
    if (progress?.isCompleted) {
      return;
    }

    // Use a more efficient interval that only updates when component is visible and lesson is not completed
    const updateTime = () => {
      if (document.visibilityState === 'visible' && !isLessonCompleted) {
        const sessionTime = Math.floor((Date.now() - stepStartTime) / 1000);
        setTimeSpent(sessionTime);
      }
    };

    const intervalId = setInterval(updateTime, 1000);

    // Also update when page becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isLessonCompleted) {
        updateTime();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isLessonCompleted, stepStartTime, progress?.isCompleted]);

  // Handle step changes with improved error handling
  const handleStepChange = useCallback(async (step: number, isLessonComplete = false) => {
    // Validate step bounds
    if (step < 0 || step >= steps.length) {
      console.warn(`Invalid step index: ${step}. Must be between 0 and ${steps.length - 1}`);
      return;
    }

    // Calculate time spent on current step
    const currentTime = Date.now();
    const stepTimeSpent = Math.floor((currentTime - stepStartTime) / 1000);

    setCurrentStep(step);
    if (onStepChange) {
      onStepChange(step);
    }

    // Skip progress updates in review mode or if lesson is already completed
    if (progress?.isCompleted) {
      setStepStartTime(currentTime);
      return;
    }

    // Save progress when navigating to a new step
    try {
      const currentCompletedSteps = progress?.completedSteps || [];
      const newCompletedSteps = [...currentCompletedSteps];

      // Mark previous steps as completed if moving forward
      if (step > currentStep) {
        for (let i = currentStep; i < step; i++) {
          if (!newCompletedSteps.includes(i)) {
            newCompletedSteps.push(i);
          }
        }
      }

      // Handle lesson completion - mark current step as completed
      if (isLessonComplete && !newCompletedSteps.includes(step)) {
        newCompletedSteps.push(step);
        setIsLessonCompleted(true); // Stop the timer
      }

      await updateProgressMutation.mutateAsync({
        contentId,
        currentStepIndex: step,
        completedSteps: newCompletedSteps,
        timeSpent: stepTimeSpent, // Send actual time spent on the step
      });
    } catch (error) {
      console.error('Failed to save progress:', error);
      // Optionally show user-friendly error message
      // You could add a toast notification here
    }

    // Update step start time for next calculation
    setStepStartTime(currentTime);
  }, [currentStep, steps.length, progress?.completedSteps, stepStartTime, contentId, onStepChange, updateProgressMutation]);

  // Sync with external step changes
  useEffect(() => {
    setCurrentStep(initialStep ?? 0);
  }, [initialStep]);

  // Reset step start time when step changes
  useEffect(() => {
    setStepStartTime(Date.now());
  }, [currentStep]);

  const completedSteps = progress?.completedSteps || [];

  // Check if lesson is completed when all steps are done (skip in review mode or if already completed)
  useEffect(() => {
    if (progress?.isCompleted) {
      return;
    }

    const allStepsCompleted = completedSteps.length === steps.length && steps.length > 0;
    if (allStepsCompleted && !isLessonCompleted) {
      setIsLessonCompleted(true);
    }
    // Reset completion status if not all steps are completed
    if (!allStepsCompleted && isLessonCompleted) {
      setIsLessonCompleted(false);
    }
  }, [completedSteps.length, steps.length, isLessonCompleted]);
  const completionPercentage = useMemo(() => {
    if (isLessonCompleted) {
      return 100;
    }
    return Math.round((completedSteps.length / steps.length) * 100);
  }, [completedSteps.length, steps.length, isLessonCompleted]);
  const bookmarks = progress?.bookmarks || [];
  const notes = progress?.notes || [];

  // Trophy animation logic with cleanup
  const triggerTrophyAnimation = useCallback((stepIndex: number) => {
    setTrophyAnimations(prev => new Set([...prev, stepIndex]));

    const timeoutId = setTimeout(() => {
      setTrophyAnimations(prev => {
        const newSet = new Set(prev);
        newSet.delete(stepIndex);
        return newSet;
      });
    }, 2000); // Animation duration

    // Store timeout ID for potential cleanup
    return () => clearTimeout(timeoutId);
  }, []);

  // We no longer need to detect newly completed steps here
  // Trophy animations are now triggered only after successful progress save
  // in the updateProgressMutation.onSuccess callback

  // Current step bookmark and note status with memoization
  const isStepBookmarked = useMemo(() => {
    return bookmarks.some((b: { stepIndex: number }) => b.stepIndex === currentStep);
  }, [bookmarks, currentStep]);

  const stepNotes = useMemo(() => {
    return notes.filter((n: { stepIndex: number }) => n.stepIndex === currentStep);
  }, [notes, currentStep]);

  // Format time
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  // Restart lesson functionality
  const handleRestartLesson = useCallback(async () => {
    try {
      // Reset all states
      setCurrentStep(0);
      setTimeSpent(0);
      setStepStartTime(Date.now());
      setIsLessonCompleted(false);
      setTrophyAnimations(new Set());

      // Reset progress in database
      await updateProgressMutation.mutateAsync({
        contentId,
        currentStepIndex: 0,
        completedSteps: [],
        timeSpent: 0,
      });

      // Call external onStepChange if provided
      if (onStepChange) {
        onStepChange(0);
      }
    } catch (error) {
      console.error('Failed to restart lesson:', error);
    }
  }, [contentId, updateProgressMutation, onStepChange]);

  // Bookmark toggle handler
  const handleToggleBookmark = useCallback(async () => {
    // Prevent bookmark changes in review mode
    if (progress?.isCompleted) {
      return;
    }

    try {
      if (isStepBookmarked) {
        await removeBookmarkMutation.mutateAsync({
          contentId,
          stepIndex: currentStep
        });
      } else {
        await addBookmarkMutation.mutateAsync({
          contentId,
          stepIndex: currentStep
        });
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      // Optionally show user-friendly error message
    }
  }, [isStepBookmarked, contentId, currentStep, removeBookmarkMutation, addBookmarkMutation]);

  // Note handlers
  const handleAddNote = () => {
    // Prevent note changes in review mode
    if (progress?.isCompleted) {
      return;
    }

    setShowNoteDialog(true);
    setNoteContent('');
    setEditingNoteIndex(null);
  };

  const handleSaveNote = async () => {
    if (!noteContent.trim()) return;

    // Prevent note changes in review mode
    if (progress?.isCompleted) {
      return;
    }

    if (editingNoteIndex !== null) {
      // Update existing note
      await updateNoteMutation.mutateAsync({
        contentId,
        content: noteContent,
        stepIndex: currentStep,
        noteIndex: editingNoteIndex
      });
    } else {
      // Add new note
      await addNoteMutation.mutateAsync({
        contentId,
        stepIndex: currentStep,
        content: noteContent
      });
    }

    setShowNoteDialog(false);
    setNoteContent('');
    setEditingNoteIndex(null);
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (!steps || steps.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800">
        <div className="text-center text-gray-500 dark:text-gray-400">
          No steps provided
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Enhanced Progress Indicator with Integrated Elements */}
      <div className="flex items-center justify-between p-4 pb-3 flex-shrink-0 border-b dark:border-gray-700 bg-white dark:bg-gray-800">
        {/* Left: Learning Progress */}
        <div className={cn(
          "flex items-center space-x-3 text-xs text-gray-600 dark:text-gray-300"
        )}>
          <div className="flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{formatTime(progress?.isCompleted ? totalTimeSpent : timeSpent)}</span>
            {progress?.isCompleted && (
              <span className="text-gray-500 dark:text-gray-400">(Total)</span>
            )}
          </div>
          {progress?.isCompleted && (
            <div className="flex items-center space-x-1">
              <span>Sessions: {progress?.sessionCountComplete || 0}</span>
            </div>
          )}
          <div className={cn(
            "font-medium",
            isLessonCompleted
              ? "text-green-600 dark:text-green-400"
              : "text-blue-600 dark:text-blue-400"
          )}>
            {completionPercentage}%
            {isLessonCompleted && (
              <span className="ml-1 text-green-600 dark:text-green-400">✓ Complete</span>
            )}
          </div>
          <div>
            Step {currentStep + 1}/{steps.length}
          </div>
        </div>

        {/* Center: Step Navigation Dots with Completion Status */}
        <div className={cn(
          "flex space-x-2",
        )}>
          {steps.map((_, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;
            const showTrophyAnimation = trophyAnimations.has(index);
            return (
              <div
                key={index}
                className={cn(
                  "w-3 h-3 rounded-full cursor-pointer transition-all duration-200 flex items-center justify-center relative",
                  isCompleted
                    ? 'bg-green-500 dark:bg-green-400'
                    : isCurrent
                    ? 'bg-blue-500 dark:bg-blue-400'
                    : 'bg-gray-300 dark:bg-gray-600'
                )}
                onClick={() => handleStepChange(index)}
              >
                {isCompleted && !showTrophyAnimation && (
                  <Check className="h-2 w-2 text-white" />
                )}
                {showTrophyAnimation && (
                  <div className="absolute -top-0 left-1/2 transform -translate-x-1/2 z-10">
                    <Trophy className="h-4 w-4 text-yellow-400 animate-trophy-jump drop-shadow-lg" />
                    <div className="absolute inset-0 rounded-full bg-yellow-400 opacity-20 animate-ping scale-200" />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Right: Action Buttons and Quiz History */}
        <div className={cn(
          "flex items-center space-x-3 text-xs text-gray-600 dark:text-gray-300",
        )}>
          {/* Restart Button - only show when lesson is completed */}
          {progress?.isCompleted && (
            <button
              onClick={handleRestartLesson}
              className="flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors text-green-600 dark:text-green-400"
              title="Restart lesson"
            >
              <RotateCcw className="h-3 w-3" />
              <span>Restart</span>
            </button>
          )}

          {/* Bookmark Button */}
          <button
            onClick={handleToggleBookmark}
            disabled={progress?.isCompleted}
            className={cn(
              "flex items-center space-x-1 px-2 py-1 rounded transition-colors",
              progress?.isCompleted
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-gray-100 dark:hover:bg-gray-700",
              isStepBookmarked && "text-yellow-600 dark:text-yellow-400"
            )}
            title={progress?.isCompleted ? "Bookmarks disabled in review mode" : (isStepBookmarked ? "Remove bookmark" : "Bookmark this step")}
          >
            <Bookmark className={cn("h-3 w-3", isStepBookmarked && "fill-current")} />
          </button>

          {/* Note Button */}
          <button
            onClick={handleAddNote}
            disabled={progress?.isCompleted}
            className={cn(
              "flex items-center space-x-1 px-2 py-1 rounded transition-colors relative",
              progress?.isCompleted
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-gray-100 dark:hover:bg-gray-700",
              stepNotes.length > 0 && "text-purple-600 dark:text-purple-400"
            )}
            title={progress?.isCompleted ? "Notes disabled in review mode" : "Add note to this step"}
          >
            <StickyNote className="h-3 w-3" />
            {stepNotes.length > 0 && (
              <span className="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {stepNotes.length}
              </span>
            )}
          </button>

          {stats.completedAttempts > 0 ? (
            <Popover key={`quiz-history-${contentId}`}>
              <PopoverTrigger asChild>
                <button className="flex items-center space-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors">
                  <div>
                    {(() => {
                      // Calculate unique quiz count
                      const uniqueQuizIds = new Set(quizHistory.map(quiz => quiz.quizId));
                      const quizCount = uniqueQuizIds.size;
                      return `${quizCount} quiz${quizCount !== 1 ? 'zes' : ''}`;
                    })()}
                  </div>
                  <MoreVertical className="h-3 w-3" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-96 p-4" align="end">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">Quiz History</h3>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {stats.completedAttempts} attempt{stats.completedAttempts !== 1 ? 's' : ''}
                    </div>
                  </div>

                  {/* Removed global Best Score Display */}

                  <div className="space-y-3 max-h-80 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded p-2">
                    {(() => {
                      // Group attempts by quizId and sort by date
                      const groupedQuizzes = quizHistory.reduce((acc, quiz) => {
                        const quizId = quiz.quizId;
                        const title = quiz.quizTitle || 'Untitled Quiz';
                        if (!acc[quizId]) {
                          acc[quizId] = {
                            title,
                            attempts: []
                          };
                        }
                        acc[quizId].attempts.push(quiz);
                        return acc;
                      }, {} as Record<string, { title: string; attempts: typeof quizHistory }>);

                      // Sort attempts within each quiz by date (oldest first) and calculate best score
                      Object.values(groupedQuizzes).forEach(group => {
                        group.attempts.sort((a, b) => new Date(a.startedAt).getTime() - new Date(b.startedAt).getTime());
                      });

                      return Object.entries(groupedQuizzes).map(([quizId, { title, attempts }]) => {
                        // Calculate best score for this quiz
                        const bestScore = Math.max(
                          ...attempts
                            .filter(attempt => attempt.isCompleted && attempt.score?.percentage !== undefined)
                            .map(attempt => attempt.score!.percentage!)
                        );
                        const hasBestScore = attempts.some(attempt => attempt.isCompleted && attempt.score?.percentage !== undefined);

                        return (
                          <div key={quizId} className="space-y-2">
                            {/* Quiz Title Header with Best Score */}
                            <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-1">
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {title}
                              </div>
                              {hasBestScore && (
                                <div className="flex items-center space-x-1">
                                  <Trophy className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
                                  <span className="text-xs font-medium text-yellow-800 dark:text-yellow-200">
                                    Best: {bestScore.toFixed(0)}%
                                  </span>
                                </div>
                              )}
                            </div>

                            {/* Quiz Attempts */}
                            <div className="space-y-1 ml-2">
                              {attempts.map((quiz, attemptIndex) => (
                                <div key={quiz.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-2">
                                      <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                        Attempt #{attemptIndex + 1}:
                                      </span>
                                      {quiz.isCompleted && (
                                        <span className="text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                                          {quiz.score?.percentage?.toFixed(0)}%
                                        </span>
                                      )}
                                      <div className="text-xs text-gray-500 dark:text-gray-400">
                                        ({formatDate(quiz.startedAt)})
                                      </div>
                                    </div>
                                  </div>

                                  {quiz.isCompleted && (
                                    <div className="flex space-x-1">
                                      <button
                                        onClick={() => onQuizSelect?.(quiz.quizId, quiz.id)}
                                        className="p-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900 rounded"
                                        title="Review quiz"
                                      >
                                        <Eye className="h-3 w-3" />
                                      </button>
                                      <button
                                        onClick={() => onRetakeQuiz?.(quiz.quizId)}
                                        className="p-1 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900 rounded"
                                        title="Retake quiz"
                                      >
                                        <RotateCcw className="h-3 w-3" />
                                      </button>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      });
                    })()}

                    {/* Load More Button */}
                    {hasMoreQuizzes && (
                      <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                        <button
                          onClick={handleLoadMore}
                          disabled={isLoadingMore}
                          className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isLoadingMore ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Loading...</span>
                            </>
                          ) : (
                            <span>Load More</span>
                          )}
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={onGenerateNew}
                      className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                    >
                      <FileQuestion className="h-4 w-4" />
                      <span>Generate New Quiz</span>
                    </button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          ) : (
            <button
              onClick={onGenerateNew}
              className="flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              <FileQuestion className="h-3 w-3" />
              <span>Generate Quiz</span>
            </button>
          )}
        </div>
      </div>

      {/* MultiStepExplain Component - Takes remaining space */}
      <div className="flex-1 min-h-0">
        <div className="h-full">
          <MultiStepExplain
            steps={steps}
            initialStep={currentStep}
            onStepChange={handleStepChange}
            completedSteps={completedSteps}
            className="h-full [&>div:first-child]:hidden"
          />
        </div>
      </div>

      {/* Note Dialog */}
      {showNoteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              {editingNoteIndex !== null ? 'Edit Note' : 'Add Note'} for Step {currentStep + 1}
            </h3>
            <textarea
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder="Enter your note..."
              className="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => {
                  setShowNoteDialog(false);
                  setNoteContent('');
                  setEditingNoteIndex(null);
                }}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveNote}
                disabled={!noteContent.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
              >
                {editingNoteIndex !== null ? 'Update' : 'Save'} Note
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
