import type { ActionFunctionArgs, LoaderFunction<PERSON>rgs, MetaFunction } from "react-router";
import { Form, Link, useActionData, useNavigation } from "react-router";
import { redirect } from "react-router";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { getSession } from "~/lib/session.server";
import { auth } from "~/lib/auth.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Register - Kwaci Learning" },
    { name: "description", content: "Create your Kwaci Learning account" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (session) {
    return redirect("/dashboard");
  }
  
  return null;
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const name = formData.get("name")?.toString();
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const confirmPassword = formData.get("confirmPassword")?.toString();

  if (!name || !email || !password || !confirmPassword) {
    return {
      error: "All fields are required",
    };
  }

  if (password !== confirmPassword) {
    return {
      error: "Passwords do not match",
    };
  }

  if (password.length < 8) {
    return {
      error: "Password must be at least 8 characters long",
    };
  }

  try {
    // Create a new request for the auth handler with the sign-up data
    const authRequest = new Request(new URL("/api/auth/sign-up/email", request.url), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Forward original request headers (including cookies)
        ...Object.fromEntries(request.headers.entries()),
      },
      body: JSON.stringify({ name, email, password }),
    });

    // Use auth.handler directly to ensure proper cookie handling
    const response = await auth.handler(authRequest);

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: "Failed to create account" }));
      return {
        error: error.message || "Failed to create account",
      };
    }

    // If successful, redirect to dashboard
    // The auth.handler will have set the session cookies in the response
    return redirect("/dashboard", {
      headers: response.headers,
    });
  } catch (error) {
    console.error("Registration error:", error);
    return {
      error: "An unexpected error occurred. Please try again.",
    };
  }
}

export default function Register() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl text-center">Create account</CardTitle>
          <CardDescription className="text-center">
            Enter your information to create your Kwaci Learning account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form method="post" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                type="text"
                placeholder="Enter your full name"
                required
                disabled={isSubmitting}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email"
                required
                disabled={isSubmitting}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Create a password"
                required
                disabled={isSubmitting}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="Confirm your password"
                required
                disabled={isSubmitting}
              />
            </div>
            {actionData?.error && (
              <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                {actionData.error}
              </div>
            )}
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "Creating account..." : "Create account"}
            </Button>
          </Form>
          <div className="mt-4 text-center text-sm">
            Already have an account?{" "}
            <Link to="/login" className="text-primary hover:underline">
              Sign in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}