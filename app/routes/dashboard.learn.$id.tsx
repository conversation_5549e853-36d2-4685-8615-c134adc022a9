import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { use<PERSON><PERSON>der<PERSON><PERSON>, Link, useNavigate } from "react-router";
import { useState } from "react";
import { requireAuthSession } from "~/lib/session.server";
import { ServerApiClient } from "~/lib/data-fetching";
import type { LearningContent } from "~/db/schema/learning-content";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { EnhancedLearningContentDisplay } from "~/components/learn/EnhancedLearningContentDisplay";
import { Button } from "~/components/ui/button";
import { QuizSelectionModal } from "~/components/quiz/QuizSelectionModal";
import { useGenerateQuiz } from "~/lib/hooks/use-quiz-api";
import { ArrowLeft, Zap } from "lucide-react";
import type { QuizGenerationConfig } from "~/components/quiz/types";

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data?.content?.title || "Learning Content"} - Kwaci Learning` },
    { name: "description", content: data?.content?.description || "View your learning content" },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const { id } = params;

  if (!id) {
    throw new Response("Content ID is required", { status: 400 });
  }

  try {
    // ✅ Use typed API client for type-safe data fetching
    const content: LearningContent = await ServerApiClient.getLearningContent(request, id);

    return {
      user: authSession.user,
      content,
    };
  } catch (error) {
    console.error("Error loading learning content:", error);
    throw error;
  }
}

export default function LearnContentPage() {
  const { user, content } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [isQuizModalOpen, setIsQuizModalOpen] = useState(false);
  
  // Quiz generation hooks
  const generateQuizMutation = useGenerateQuiz();

  // Transform database steps to component-expected format
  const transformedSteps = content.steps.map(step => {
    // If step has multiple blocks, we need to handle them differently
    if (step.blocks.length === 1) {
      const block = step.blocks[0];
      // Ensure the type is one of the valid StepConfig types
      const validTypes = ['paragraph', 'infoBox', 'bulletList', 'numberedList', 'grid', 'comparison', 'table', 'scatterPlot', 'keyValueGrid'] as const;
      const blockType = validTypes.includes(block.type as any) ? block.type as typeof validTypes[number] : 'paragraph';
      
      return {
        title: step.title,
        icon: step.icon || '📚',
        type: blockType,
        data: block.data
      };
    } else {
      // Multiple blocks - for now, default to paragraph with combined text
      return {
        title: step.title,
        icon: step.icon || '📚',
        type: 'paragraph' as const,
        data: step.blocks
          .map(block => typeof block.data === 'string' ? block.data : JSON.stringify(block.data))
          .join('\n')
      };
    }
  });

  const handleStepChange = (step: number) => {
    // TODO: Implement progress tracking
    console.log('Step changed to:', step);
  };

  const handleProgressUpdate = (progress: number) => {
    // TODO: Implement progress update
    console.log('Progress updated to:', progress);
  };

  const handleQuizGenerate = () => {
    setIsQuizModalOpen(true);
  };

  const handleQuizGenerateSubmit = async (config: QuizGenerationConfig) => {
    try {
      const result = await generateQuizMutation.mutateAsync(config);
      
      if (result.success && result.data?.quizId) {
        // Navigate to the generated quiz
        navigate(`/dashboard/quiz/${result.data.quizId}`);
      } else {
        console.error('Quiz generation failed:', result.error);
      }
    } catch (error) {
      console.error('Quiz generation error:', error);
    } finally {
      setIsQuizModalOpen(false);
    }
  };

  return (
    <DashboardLayout>
      {/* Back Navigation and Quiz Generation */}
      <div className="flex items-center justify-between mb-6">
        <Link to="/dashboard/learn">
          <Button variant="ghost" size="sm" className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Learning</span>
          </Button>
        </Link>
        
        <Button 
          onClick={handleQuizGenerate} 
          className="flex items-center space-x-2"
          disabled={generateQuizMutation.isPending}
        >
          <Zap className="h-4 w-4" />
          <span>Generate Quiz</span>
        </Button>
      </div>

      {/* Enhanced Learning Content Display */}
      <EnhancedLearningContentDisplay
        contentId={content.id}
        title={content.title}
        description={content.description}
        steps={transformedSteps}
        learningLevel={content.learningLevel}
        estimatedReadingTime={content.estimatedReadingTime}
        isPublic={content.isPublic}
        initialStep={0}
        completedSteps={[]}
        progress={0}
        onStepChange={handleStepChange}
        onProgressUpdate={handleProgressUpdate}
      />

      {/* Quiz Selection Modal */}
      <QuizSelectionModal
        isOpen={isQuizModalOpen}
        onClose={() => setIsQuizModalOpen(false)}
        onGenerate={handleQuizGenerateSubmit}
        learningContentId={content.id}
        isGenerating={generateQuizMutation.isPending}
      />
    </DashboardLayout>
  );
}
