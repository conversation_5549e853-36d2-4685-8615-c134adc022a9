import { type LoaderFunctionArgs, type ActionFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { 
  getMyLearningContentWithProgress,
  deleteLearningContent,
  toggleLearningContentPublic,
  type MyLearningFilters 
} from "~/db/services/learning-content";
import { requireAuthSession } from "~/lib/session.server";

// Validation schemas
const myLearningFiltersSchema = z.object({
  search: z.string().optional(),
  learningLevel: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  contentType: z.enum(["standard", "kwaci-primer"]).optional(),
  tags: z.array(z.string()).optional(),
  completionStatus: z.enum(["not-started", "in-progress", "completed"]).optional(),
  dateRange: z.object({
    from: z.string().transform(str => new Date(str)),
    to: z.string().transform(str => new Date(str)),
  }).optional(),
  readingTimeRange: z.object({
    min: z.number().min(0),
    max: z.number().min(0),
  }).optional(),
  isPublic: z.boolean().optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "title", "progress"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional(),
});

const actionSchema = z.object({
  action: z.enum(["delete", "togglePublic"]),
  contentId: z.string().min(1, "Content ID is required"),
});

// GET /api/my-learning - Get user's learning content with progress
export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  try {
    // Parse query parameters
    const rawFilters: Record<string, any> = {};
    
    // Extract search params
    const search = url.searchParams.get('search');
    if (search) rawFilters.search = search;
    
    const learningLevel = url.searchParams.get('learningLevel');
    if (learningLevel) rawFilters.learningLevel = learningLevel;
    
    const contentType = url.searchParams.get('contentType');
    if (contentType) rawFilters.contentType = contentType;
    
    const tags = url.searchParams.get('tags');
    if (tags) rawFilters.tags = tags.split(',');
    
    const completionStatus = url.searchParams.get('completionStatus');
    if (completionStatus) rawFilters.completionStatus = completionStatus;
    
    const isPublic = url.searchParams.get('isPublic');
    if (isPublic) rawFilters.isPublic = isPublic === 'true';
    
    const sortBy = url.searchParams.get('sortBy');
    if (sortBy) rawFilters.sortBy = sortBy;
    
    const sortOrder = url.searchParams.get('sortOrder');
    if (sortOrder) rawFilters.sortOrder = sortOrder;
    
    const limit = url.searchParams.get('limit');
    if (limit) rawFilters.limit = parseInt(limit, 10);
    
    const offset = url.searchParams.get('offset');
    if (offset) rawFilters.offset = parseInt(offset, 10);
    
    // Date range handling
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');
    if (dateFrom && dateTo) {
      rawFilters.dateRange = { from: dateFrom, to: dateTo };
    }
    
    // Reading time range handling
    const readingTimeMin = url.searchParams.get('readingTimeMin');
    const readingTimeMax = url.searchParams.get('readingTimeMax');
    if (readingTimeMin && readingTimeMax) {
      rawFilters.readingTimeRange = { 
        min: parseInt(readingTimeMin, 10), 
        max: parseInt(readingTimeMax, 10) 
      };
    }

    // Validate filters
    const filters = myLearningFiltersSchema.parse(rawFilters);

    // Get user's learning content with progress
    const result = await getMyLearningContentWithProgress(
      db, 
      authSession.user.id, 
      filters as MyLearningFilters
    );

    return Response.json({ 
      success: true, 
      data: result 
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Invalid filters", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Error fetching my learning content:", error);
    return Response.json(
      { success: false, error: "Failed to fetch learning content" },
      { status: 500 }
    );
  }
}

// POST /api/my-learning - Handle content management actions
export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  try {
    const body = await request.json();
    const { action, contentId } = actionSchema.parse(body);

    switch (action) {
      case "delete":
        await deleteLearningContent(db, contentId, authSession.user.id);
        return Response.json({ 
          success: true, 
          message: "Content deleted successfully" 
        });

      case "togglePublic":
        const updatedContent = await toggleLearningContentPublic(
          db, 
          contentId, 
          authSession.user.id
        );
        return Response.json({ 
          success: true, 
          data: updatedContent,
          message: `Content is now ${updatedContent.isPublic ? 'public' : 'private'}` 
        });

      default:
        return Response.json(
          { success: false, error: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof Error && error.message === 'Content not found or access denied') {
      return Response.json(
        { success: false, error: error.message },
        { status: 404 }
      );
    }
    
    console.error("Error processing my learning action:", error);
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}
