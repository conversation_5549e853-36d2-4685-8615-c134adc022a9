import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "react-router";
import { useLoaderData, Form } from "react-router";
import { redirect } from "react-router";
import { requireAuthSession } from "~/lib/session.server";
import { auth } from "~/lib/auth.server";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Dashboard - Kwaci Learning" },
    { name: "description", content: "Your learning dashboard" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  console.log('[Dashboard] Loader called for:', request.url);
  console.log('[Dashboard] Request headers:', Object.fromEntries(request.headers.entries()));

  try {
    const authContext = await requireAuthSession(request);
    console.log('[Dashboard] Auth context:', { userId: authContext.user.id, email: authContext.user.email });

    // Generate consistent data for SSR/hydration
    const skillAreas = [
      { name: "Skill Area 1", progress: 86 },
      { name: "Skill Area 2", progress: 25 },
      { name: "Skill Area 3", progress: 33 },
      { name: "Skill Area 4", progress: 66 },
      { name: "Skill Area 5", progress: 88 },
    ];

    const currentDate = new Date().toLocaleDateString();

    return {
      user: authContext.user,
      skillAreas,
      currentDate,
    };
  } catch (error) {
    console.error('[Dashboard] Auth error:', error);
    throw error;
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "logout") {
    try {
      // Call the better-auth sign-out endpoint using auth.handler
      const authRequest = new Request(new URL("/api/auth/sign-out", request.url), {
        method: "POST",
        headers: {
          // Forward original request headers (including cookies)
          ...Object.fromEntries(request.headers.entries()),
        },
      });

      const response = await auth.handler(authRequest);

      if (response.ok) {
        // Include response headers (for cookie clearing) in the redirect
        return redirect("/login", {
          headers: response.headers,
        });
      }
    } catch (error) {
      console.error("Logout error:", error);
    }
  }

  return null;
}

export default function Dashboard() {
  const { user, skillAreas, currentDate } = useLoaderData<typeof loader>();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <h1 className="text-2xl font-bold">Dashboard</h1>
        </div>
        
        {/* Main Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Welcome back!</CardTitle>
              <CardDescription>
                Hello, {user.name || user.email}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                You're successfully logged in to your learning dashboard.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Learning Progress</CardTitle>
              <CardDescription>
                Your current learning metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Completed Lessons</span>
                  <span className="font-semibold">0</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Active Courses</span>
                  <span className="font-semibold">0</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total Study Hours</span>
                  <span className="font-semibold">0h</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Get started with your learning journey
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                Start New Course
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Continue Learning
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Take Quiz
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity Section */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest learning activities and achievements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 8 }, (_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold">{i + 1}</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">Sample Activity {i + 1}</h4>
                    <p className="text-sm text-muted-foreground">
                      This is a sample activity item to demonstrate scrolling behavior. 
                      The header should remain fixed when you scroll through this content.
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {i + 1} hour{i !== 0 ? 's' : ''} ago
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Course Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle>Recommended Courses</CardTitle>
            <CardDescription>
              Courses tailored to your learning preferences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Array.from({ length: 6 }, (_, i) => (
                <div key={i} className="border rounded-lg p-4 space-y-2">
                  <h4 className="font-medium">Sample Course {i + 1}</h4>
                  <p className="text-sm text-muted-foreground">
                    This is a sample course description that provides enough content 
                    to test the scrolling behavior of the dashboard layout.
                  </p>
                  <div className="flex justify-between items-center pt-2">
                    <span className="text-xs text-muted-foreground">4.5 ⭐ (120 reviews)</span>
                    <Button size="sm" variant="outline">Enroll</Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Learning Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Learning Statistics</CardTitle>
            <CardDescription>
              Detailed breakdown of your learning journey
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {skillAreas.map((skillArea, i) => (
                <div key={i} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{skillArea.name}</span>
                    <span className="font-semibold">{skillArea.progress}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{ width: `${skillArea.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Progress in this skill area over the last 30 days. Keep up the great work!
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Footer Actions */}
        <div className="flex justify-between items-center pt-8 border-t">
          <div className="text-sm text-muted-foreground">
            Last updated: {currentDate}
          </div>
          <Form method="post">
            <input type="hidden" name="intent" value="logout" />
            <Button type="submit" variant="outline">
              Sign Out
            </Button>
          </Form>
        </div>
      </div>
    </DashboardLayout>
  );
}