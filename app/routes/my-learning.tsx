import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { useState } from "react";
import { Link } from "react-router";
import { 
  Grid, 
  List, 
  Plus, 
  BookOpen, 
  AlertCircle, 
  RefreshCw,
  Filter,
  Search
} from "lucide-react";
import { requireAuthSession } from "~/lib/session.server";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { 
  LearningContentCard, 
  AdvancedSearchFilters, 
  ShareContentModal 
} from "~/components/my-learning";
import { 
  useGetMyLearningContent, 
  useDeleteMyLearningContent, 
  useToggleContentPublic 
} from "~/lib/hooks/use-learning-api";
import type { MyLearningFilters, LearningContentWithProgress } from "~/db/services/learning-content";

export const meta: MetaFunction = () => {
  return [
    { title: "My Learning - Kwaci Learning" },
    { name: "description", content: "Manage and view your learning content" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Ensure user is authenticated
  await requireAuthSession(request);
  return null;
}

export default function MyLearningPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState<MyLearningFilters>({
    limit: 20,
    offset: 0,
  });
  const [shareContent, setShareContent] = useState<LearningContentWithProgress | null>(null);

  // React Query hooks
  const { 
    data: learningData, 
    isLoading, 
    error, 
    refetch 
  } = useGetMyLearningContent(filters);

  const deleteContentMutation = useDeleteMyLearningContent();
  const togglePublicMutation = useToggleContentPublic();

  const handleFiltersChange = (newFilters: MyLearningFilters) => {
    setFilters({ ...newFilters, offset: 0 }); // Reset offset when filters change
  };

  const handleDelete = async (contentId: string) => {
    if (window.confirm('Are you sure you want to delete this learning content? This action cannot be undone.')) {
      try {
        await deleteContentMutation.mutateAsync(contentId);
      } catch (error) {
        console.error('Failed to delete content:', error);
      }
    }
  };

  const handleTogglePublic = async (contentId: string) => {
    try {
      await togglePublicMutation.mutateAsync(contentId);
    } catch (error) {
      console.error('Failed to toggle content visibility:', error);
    }
  };

  const handleShare = (content: LearningContentWithProgress) => {
    setShareContent(content);
  };

  const handleLoadMore = () => {
    setFilters(prev => ({
      ...prev,
      offset: (prev.offset || 0) + (prev.limit || 20),
    }));
  };

  const content = learningData?.content || [];
  const total = learningData?.total || 0;
  const hasMore = learningData?.hasMore || false;

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              My Learning
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage and track your learning content
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Link to="/dashboard/learn">
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create New
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats */}
        {!isLoading && content.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Total Content</p>
                    <p className="text-2xl font-bold">{total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <div className="h-5 w-5 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                    <div className="h-2 w-2 bg-green-600 rounded-full" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
                    <p className="text-2xl font-bold">
                      {content.filter(c => c.isCompleted).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <div className="h-5 w-5 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                    <div className="h-2 w-2 bg-blue-600 rounded-full" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">In Progress</p>
                    <p className="text-2xl font-bold">
                      {content.filter(c => c.progressId && !c.isCompleted).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <AdvancedSearchFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              className="sticky top-4"
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* View Controls */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? 'Loading...' : `${content.length} of ${total} items`}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Error State */}
            {error && (
              <Alert className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="flex items-center justify-between">
                  <span>Failed to load learning content. Please try again.</span>
                  <Button variant="outline" size="sm" onClick={() => refetch()}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded" />
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && content.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">No learning content found</CardTitle>
                  <CardDescription className="mb-4">
                    {filters.search || filters.learningLevel || filters.contentType
                      ? "Try adjusting your filters or search terms."
                      : "Get started by creating your first learning content."
                    }
                  </CardDescription>
                  <div className="flex items-center justify-center gap-2">
                    {(filters.search || filters.learningLevel || filters.contentType) && (
                      <Button
                        variant="outline"
                        onClick={() => setFilters({ limit: 20, offset: 0 })}
                      >
                        Clear Filters
                      </Button>
                    )}
                    <Link to="/dashboard/learn">
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Content
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Content Grid/List */}
            {!isLoading && content.length > 0 && (
              <>
                <div className={
                  viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
                    : "space-y-4"
                }>
                  {content.map((item) => (
                    <LearningContentCard
                      key={item.id}
                      content={item}
                      viewMode={viewMode}
                      onDelete={handleDelete}
                      onShare={handleShare}
                      onTogglePublic={handleTogglePublic}
                    />
                  ))}
                </div>

                {/* Load More */}
                {hasMore && (
                  <div className="text-center mt-8">
                    <Button
                      variant="outline"
                      onClick={handleLoadMore}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Loading...' : 'Load More'}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Share Modal */}
        <ShareContentModal
          content={shareContent}
          isOpen={!!shareContent}
          onClose={() => setShareContent(null)}
          onTogglePublic={handleTogglePublic}
        />
      </div>
    </DashboardLayout>
  );
}
