import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "react-router";
import { useLoaderData, useActionData, useNavigation, Form, Link } from "react-router";
import { useState, useEffect } from "react";
import { z } from "zod";
import { requireAuthSession } from "~/lib/session.server";
import { ServerApiClient } from "~/lib/data-fetching";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { LearningInputForm } from "~/components/learn";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { ArrowLeft, Lightbulb, Zap, Target, CheckCircle, AlertCircle, Clock, Brain, Sparkles } from "lucide-react";

export const meta: MetaFunction = () => {
  return [
    { title: "Create Learning Content - Kwaci Learning" },
    { name: "description", content: "Generate personalized learning content powered by AI" },
  ];
};

// Form data validation schema
const learningFormSchema = z.object({
  topic: z.string().min(3, "Topic must be at least 3 characters"),
  learningLevel: z.enum(["beginner", "intermediate", "advanced"]),
  preferredContentTypes: z.array(z.string()).min(1, "Please select at least one content type"),
  focusAreas: z.string().optional(),
  recommendationId: z.string().optional(),
});

type FormData = z.infer<typeof learningFormSchema>;

export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  // Extract URL parameters for pre-filling form
  const initialFormValues = {
    topic: url.searchParams.get("topic") || "",
    learningLevel: url.searchParams.get("learningLevel") as "beginner" | "intermediate" | "advanced" || "beginner",
    preferredContentTypes: url.searchParams.get("preferredContentTypes")?.split(",") || [],
    focusAreas: url.searchParams.get("focusAreas") || "",
    recommendationId: url.searchParams.get("recommendationId") || "",
  };

  return {
    user: authSession.user,
    initialFormValues,
  };
}

export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return { success: false, error: "Method not allowed" };
  }

  try {
    const formData = await request.formData();
    const intent = formData.get("intent");

    if (intent === "generate") {
      // Parse form data
      const data = {
        topic: formData.get("topic") as string,
        learningLevel: formData.get("learningLevel") as string,
        preferredContentTypes: formData.getAll("preferredContentTypes") as string[],
        focusAreas: formData.get("focusAreas") as string || undefined,
        recommendationId: formData.get("recommendationId") as string || undefined,
      };

      // Validate input
      const validatedData = learningFormSchema.parse(data);

      // Call content generation API
      const generateResult = await ServerApiClient.generateContent(request, {
        topic: validatedData.topic,
        learningLevel: validatedData.learningLevel,
        preferredContentTypes: validatedData.preferredContentTypes,
        additionalContext: validatedData.focusAreas,
      });

      // Save the generated content
      const saveResult = await ServerApiClient.saveLearningContent(request, {
        title: generateResult.title || `Learning: ${validatedData.topic}`,
        description: generateResult.description || `AI-generated learning content for ${validatedData.topic}`,
        content: {
          steps: generateResult.steps,
          estimatedReadingTime: generateResult.estimatedReadingTime,
          metadata: generateResult.metadata,
          aiMetadata: {
            ...generateResult.metadata,
            topic: validatedData.topic,
            preferredContentTypes: validatedData.preferredContentTypes,
            focusAreas: validatedData.focusAreas,
            recommendationId: validatedData.recommendationId,
          },
        },
        contentType: "standard",
        learningLevel: validatedData.learningLevel,
        estimatedReadingTime: generateResult.estimatedReadingTime,
      });

      return {
        success: true,
        contentId: saveResult.id,
        message: "Content generated successfully!",
      };
    }

    return { success: false, error: "Invalid intent" };
  } catch (error) {
    console.error("Error in learn action:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid form data",
        details: error.errors,
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}

export default function LearnPage() {
  const { user, initialFormValues } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  
  const [generationStage, setGenerationStage] = useState<"idle" | "generating" | "success" | "error">("idle");
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState("");

  const isGenerating = navigation.state === "submitting" || generationStage === "generating";

  // Handle action results
  useEffect(() => {
    if (actionData) {
      if (actionData.success) {
        setGenerationStage("success");
        setGenerationProgress(100);
        setGenerationStatus("Content generated successfully!");
        
        // Redirect to content view after delay
        setTimeout(() => {
          window.location.href = `/dashboard/learn/${actionData.contentId}`;
        }, 2000);
      } else {
        setGenerationStage("error");
        setGenerationProgress(0);
      }
    }
  }, [actionData]);

  // Initialize generation stage
  useEffect(() => {
    if (navigation.state === "submitting" && generationStage === "idle") {
      setGenerationStage("generating");
      setGenerationProgress(0);
      setGenerationStatus("Preparing to generate content...");
    }
  }, [navigation.state, generationStage]);

  // Simulate progress during generation
  useEffect(() => {
    if (generationStage === "generating") {
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) return 90;
          return prev + Math.random() * 10 + 5; // Increment by 5-15%
        });
      }, 800);

      const statusMessages = [
        "Analyzing your topic...",
        "Selecting optimal AI model...",
        "Generating content structure...",
        "Creating learning steps...",
        "Finalizing content...",
      ];

      let statusIndex = 0;
      const statusInterval = setInterval(() => {
        if (statusIndex < statusMessages.length) {
          setGenerationStatus(statusMessages[statusIndex]);
          statusIndex++;
        }
      }, 2000);

      return () => {
        clearInterval(progressInterval);
        clearInterval(statusInterval);
      };
    }
  }, [generationStage]);

  const handleFormSubmit = (data: FormData) => {
    // Form submission is handled by React Router Form component
    console.log("Form data:", data);
  };

  // Show loading state during generation
  if (generationStage === "generating") {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Link to="/dashboard">
              <Button variant="outline" disabled>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Generation Progress */}
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <div className="mb-6">
                  <Brain className="h-16 w-16 text-blue-500 mx-auto animate-pulse" />
                </div>
                <h2 className="text-2xl font-bold mb-4">
                  Generating Your Learning Content
                </h2>
                <p className="text-muted-foreground mb-6">
                  Our AI is creating personalized learning content just for you. This may take a few moments.
                </p>

                {/* Progress Bar */}
                <Progress value={generationProgress} className="mb-4" />

                {/* Status Message */}
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 animate-spin" />
                  <span>{generationStatus || "Preparing to generate content..."}</span>
                </div>

                {/* Progress Percentage */}
                <p className="text-xs text-muted-foreground mt-2">
                  {Math.round(generationProgress)}% complete
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Show success state
  if (generationStage === "success") {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Link to="/dashboard">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Success State */}
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">
                  Content Generated Successfully!
                </h2>
                <p className="text-muted-foreground mb-6">
                  Your personalized learning content is ready. You'll be redirected to view it shortly.
                </p>

                {actionData?.contentId && (
                  <Link to={`/dashboard/learn/${actionData.contentId}`}>
                    <Button className="bg-success-600 hover:bg-success-700">
                      View Your Learning Content
                    </Button>
                  </Link>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (generationStage === "error") {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Link to="/dashboard">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Error State */}
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">
                  Generation Failed
                </h2>
                <p className="text-muted-foreground mb-6">
                  {actionData?.error || "An error occurred while generating your learning content."}
                </p>

                <div className="space-x-4">
                  <Button
                    onClick={() => {
                      setGenerationStage("idle");
                      setGenerationProgress(0);
                    }}
                  >
                    Try Again
                  </Button>
                  <Link to="/dashboard/my-learning">
                    <Button variant="outline">
                      View Existing Content
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Default form state
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Link to="/dashboard">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        {/* Page Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl">Create Learning Content</CardTitle>
            <CardDescription>
              Generate personalized learning content powered by AI
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Content Type Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Standard Learning Content */}
          <Card className="border-2 border-transparent hover:border-blue-200 transition-colors">
            <CardHeader>
              <div className="flex items-center mb-4">
                <Brain className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <CardTitle className="text-lg">Standard Learning Content</CardTitle>
                  <CardDescription>Flexible, customizable learning materials</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Create personalized learning content with your choice of content types, learning level, and focus areas.
                Perfect for exploring topics in your preferred format.
              </p>
              <div className="text-sm text-muted-foreground mb-4">
                ✓ Choose your content types<br/>
                ✓ Customizable structure<br/>
                ✓ 4-8 learning steps<br/>
                ✓ Flexible format
              </div>
              <Button className="w-full">
                <Brain className="h-4 w-4 mr-2" />
                Continue with Standard Content
              </Button>
            </CardContent>
          </Card>

          {/* KWACI Primer */}
          <Card className="border-2 border-transparent hover:border-purple-200 transition-colors">
            <CardHeader>
              <div className="flex items-center mb-4">
                <Sparkles className="h-8 w-8 text-purple-600 mr-3" />
                <div>
                  <CardTitle className="text-lg">KWACI Primer</CardTitle>
                  <CardDescription>Structured 9-section comprehensive guide</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Generate a comprehensive KWACI (Knowledge With Analogy, Components, and Implementation) primer
                with a fixed 9-section structure for deep understanding.
              </p>
              <div className="text-sm text-muted-foreground mb-4">
                ✓ Fixed 9-section structure<br/>
                ✓ Analogies & examples<br/>
                ✓ Core components breakdown<br/>
                ✓ Implementation details
              </div>
              <Link to="/dashboard/learn/kwaci/new">
                <Button className="w-full bg-brand-600 hover:bg-brand-700">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Create KWACI Primer
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Tips Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-4">
              <div className="flex items-center mb-2">
                <Target className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-blue-900">Be Specific</h3>
              </div>
              <p className="text-sm text-blue-700">
                The more specific your topic, the better our AI can tailor the content to your needs.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-success/10 border-success/20">
            <CardContent className="p-4">
              <div className="flex items-center mb-2">
                <Lightbulb className="h-5 w-5 text-green-600 mr-2" />
                <h3 className="font-medium text-green-900">Choose Your Style</h3>
              </div>
              <p className="text-sm text-green-700">
                Select content types that match your learning preferences for the best experience.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-brand/10 border-brand/20">
            <CardContent className="p-4">
              <div className="flex items-center mb-2">
                <Zap className="h-5 w-5 text-purple-600 mr-2" />
                <h3 className="font-medium text-purple-900">AI-Powered</h3>
              </div>
              <p className="text-sm text-purple-700">
                Our AI creates comprehensive, step-by-step learning content tailored to your level.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Learning Input Form */}
        <Form method="post">
          <input type="hidden" name="intent" value="generate" />
          <LearningInputForm
            onSubmit={handleFormSubmit}
            isLoading={isGenerating}
            initialValues={initialFormValues}
          />
        </Form>

        {/* Example Topics */}
        <Card className="bg-muted/50">
          <CardContent className="p-6">
            <h3 className="text-lg font-medium mb-4">Need inspiration? Try these topics:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {[
                "How does machine learning work?",
                "JavaScript async/await explained",
                "Photosynthesis process",
                "Blockchain technology basics",
                "React hooks tutorial",
                "Climate change causes",
                "Database normalization",
                "Investment strategies for beginners",
                "Human digestive system"
              ].map((topic, index) => (
                <button
                  key={index}
                  className="text-left p-3 bg-background border rounded-lg hover:border-primary/30 hover:bg-primary/5 transition-colors text-sm"
                  onClick={() => {
                    // TODO: Pre-fill the form with this topic
                    console.log('Selected topic:', topic);
                  }}
                  disabled={isGenerating}
                  type="button"
                >
                  {topic}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
