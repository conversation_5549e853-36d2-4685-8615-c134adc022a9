/**
 * API route for generating quizzes from learning content using AI
 */

import type { ActionFunctionArgs } from 'react-router';
import { z } from 'zod';
import { db } from '~/db';
import { requireAuthSession } from '~/lib/session.server';
import { getLearningContentById } from '~/db/services/learning-content';
import { quiz } from '~/db/schema/quiz';
import { generateQuizFromContent } from '~/lib/ai/services/quiz-generator';
import { nanoid } from 'nanoid';
import type { QuizGenerationConfig } from '~/components/quiz/types';

// Input validation schema for quiz generation
const generateQuizSchema = z.object({
  learningContentId: z.string().min(1, 'Learning content ID is required'),
  quizTypes: z.array(z.enum([
    'flashcard', 'multipleChoice', 'trueFalse', 'fillInBlank',
    'matching', 'freeText', 'ordering'
  ])).min(1, 'At least one quiz type is required'),
  difficulty: z.enum(['easy', 'medium', 'hard']),
  questionsPerType: z.number().min(1).max(10),
  includeHints: z.boolean().default(true),
  includeExplanations: z.boolean().default(true),
  shuffleQuestions: z.boolean().default(false),
  timeLimit: z.number().min(1).max(300).optional(), // in minutes
});

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== 'POST') {
    return Response.json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const authSession = await requireAuthSession(request);
    const body = await request.json();

    // Validate input
    const config = generateQuizSchema.parse(body);

    console.info('Quiz generation request:', {
      userId: authSession.user.id,
      learningContentId: config.learningContentId,
      quizTypes: config.quizTypes,
      difficulty: config.difficulty,
      questionsPerType: config.questionsPerType
    });

    // Get the learning content
    const learningContent = await getLearningContentById(db, config.learningContentId);
    if (!learningContent) {
      return Response.json({
        success: false,
        error: 'Learning content not found'
      }, { status: 404 });
    }

    // Check if user has access to this learning content
    if (learningContent.userId !== authSession.user.id && !learningContent.isPublic) {
      return Response.json({
        success: false,
        error: 'Access denied'
      }, { status: 403 });
    }

    // Generate quiz using AI service
    const generatedQuiz = await generateQuizFromContent(
      learningContent,
      config as QuizGenerationConfig,
      {
        preferCostEffective: true,
        retryOnFailure: true,
        env: process.env, // Pass environment variables for AI configuration
      }
    );

    // Save generated quiz to database
    const quizData = {
      id: nanoid(),
      title: generatedQuiz.title,
      description: generatedQuiz.description,
      learningContentId: config.learningContentId,
      difficulty: generatedQuiz.difficulty,
      estimatedDuration: generatedQuiz.estimatedDuration,
      totalPoints: generatedQuiz.totalPoints,
      questions: generatedQuiz.questions,
      metadata: generatedQuiz.metadata,
      isPublic: generatedQuiz.isPublic,
      allowRetakes: generatedQuiz.allowRetakes,
      showCorrectAnswers: generatedQuiz.showCorrectAnswers,
      shuffleQuestions: generatedQuiz.shuffleQuestions,
      timeLimit: generatedQuiz.timeLimit,
      createdBy: authSession.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const [newQuiz] = await db.insert(quiz).values(quizData).returning();

    console.info('Quiz generation completed:', {
      quizId: newQuiz.id,
      questionsGenerated: generatedQuiz.questions.length,
      estimatedDuration: generatedQuiz.estimatedDuration,
      totalPoints: generatedQuiz.totalPoints
    });

    return Response.json({
      success: true,
      data: {
        quizId: newQuiz.id,
        message: `Quiz generated successfully with ${generatedQuiz.questions.length} questions`
      }
    }, { status: 201 });
    
  } catch (error) {
    console.error('Quiz generation failed:', error);
    
    if (error instanceof z.ZodError) {
      return Response.json({
        success: false,
        error: 'Invalid input',
        details: error.errors
      }, { status: 400 });
    }
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate quiz'
    }, { status: 500 });
  }
}