/**
 * Learning content database service
 * Provides functions to save, retrieve, and manage AI-generated learning content
 * All functions are fully type-safe using Drizzle ORM's type inference
 */

import { eq, and, or, desc, asc, like, ilike, gte, lte } from 'drizzle-orm';
import {
  learningContent,
  type LearningContent,
  type NewLearningContent,
  type LearningContentListResponse,
  type LearningContentStatsResponse
} from '../schema/learning-content';
import { learningProgress } from '../schema/analytics';
import type { Database } from '../connection';
import { createLearningContentIndexingService } from '~/lib/rag';

export interface LearningContentFilters {
  userId?: string;
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  contentType?: 'standard' | 'kwaci-primer';
  isPublic?: boolean;
  tags?: string[];
  search?: string;
  query?: string; // For search functionality
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface MyLearningFilters {
  search?: string;
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  contentType?: 'standard' | 'kwaci-primer';
  tags?: string[];
  completionStatus?: 'not-started' | 'in-progress' | 'completed';
  dateRange?: { from: Date; to: Date };
  readingTimeRange?: { min: number; max: number };
  isPublic?: boolean;
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'progress';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface LearningContentWithProgress extends LearningContent {
  // Progress fields (nullable since LEFT JOIN)
  progressId?: string | null;
  currentStepIndex?: number | null;
  completedSteps?: number[] | null;
  totalTimeSpent?: number | null;
  completionPercentage?: number | null;
  isCompleted?: boolean | null;
  lastAccessedAt?: Date | null;
  sessionCount?: number | null;
}

// LearningContentStats interface moved to schema file as LearningContentStatsResponse

/**
 * Save AI-generated learning content to the database
 */
export async function saveLearningContent(
  db: Database,
  content: Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt'>
): Promise<LearningContent> {
  const contentId = crypto.randomUUID();
  const now = new Date();

  const newContent: NewLearningContent = {
    id: contentId,
    ...content,
    createdAt: now,
    updatedAt: now,
  };

  // Insert and return the created content in a single operation for better type safety
  const [createdContent] = await db
    .insert(learningContent)
    .values(newContent)
    .returning();

  // Index content for vector search (async, non-blocking)
  // Note: Using setTimeout to make this truly non-blocking
  setTimeout(async () => {
    try {
      const indexingService = createLearningContentIndexingService();

      // Transform database content to RAG service format
      const ragContent = {
        ...createdContent,
        tags: createdContent.tags || [],
        aiMetadata: createdContent.aiMetadata || undefined,
      } as any; // Type assertion for now - we'll improve this later

      const indexingResult = await indexingService.indexContent(ragContent);

      if (indexingResult.success) {
        console.log(`Successfully indexed content ${contentId}: ${indexingResult.chunksIndexed} chunks`);
      } else {
        console.warn(`Failed to index content ${contentId}:`, indexingResult.error);
      }
    } catch (error) {
      // Log error but don't fail the content creation
      console.error('Failed to index content for vector search:', error);
    }
  }, 0);

  return createdContent;
}

/**
 * Get user's learning content with filtering and pagination
 * @param db - Database connection
 * @param userId - User ID to filter by
 * @param filters - Optional filters for content
 * @returns Promise<LearningContentListResponse> - Paginated results with full type safety
 */
export async function getUserLearningContent(
  db: Database,
  userId: string,
  filters: Omit<LearningContentFilters, 'userId'> = {}
): Promise<LearningContentListResponse> {
  const whereConditions = [eq(learningContent.userId, userId)];

  // Add filters
  if (filters.learningLevel) {
    whereConditions.push(eq(learningContent.learningLevel, filters.learningLevel));
  }

  if (filters.isPublic !== undefined) {
    whereConditions.push(eq(learningContent.isPublic, filters.isPublic));
  }

  if (filters.search) {
    const searchCondition = or(
      ilike(learningContent.title, `%${filters.search}%`),
      ilike(learningContent.description, `%${filters.search}%`)
    );
    if (searchCondition) {
      whereConditions.push(searchCondition);
    }
  }

  // Build sort order
  const sortColumn = filters.sortBy === 'title' ? learningContent.title :
                    filters.sortBy === 'createdAt' ? learningContent.createdAt :
                    learningContent.updatedAt;
  const sortOrder = filters.sortOrder === 'asc' ? asc : desc;

  // Get total count
  const totalResult = await db
    .select({ count: learningContent.id })
    .from(learningContent)
    .where(and(...whereConditions));

  // Get content with pagination
  const content = await db
    .select()
    .from(learningContent)
    .where(and(...whereConditions))
    .orderBy(sortOrder(sortColumn))
    .limit(filters.limit || 20)
    .offset(filters.offset || 0);

  return {
    content,
    total: totalResult.length,
    hasMore: content.length === (filters.limit || 20),
  };
}

/**
 * Get learning content by ID with access control
 * @param db - Database connection
 * @param contentId - Content ID to retrieve
 * @param userId - Optional user ID for access control
 * @returns Promise<LearningContent | null> - The content item or null if not found/accessible
 */
export async function getLearningContentById(
  db: Database,
  contentId: string,
  userId?: string
): Promise<LearningContent | null> {
  const content = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.id, contentId))
    .limit(1);

  if (!content.length) {
    return null;
  }

  const contentItem = content[0];

  // Check access permissions
  if (!contentItem.isPublic && contentItem.userId !== userId) {
    throw new Error('Access denied to private content');
  }

  return contentItem;
}

/**
 * Search learning content across all public content and user's private content
 * @param db - Database connection
 * @param filters - Search and filter criteria
 * @returns Promise<LearningContentListResponse> - Search results with full type safety
 */
export async function searchLearningContent(
  db: Database,
  filters: LearningContentFilters
): Promise<LearningContentListResponse> {
  const whereConditions = [];

  // Access control: public content + user's private content
  if (filters.userId) {
    whereConditions.push(
      or(
        eq(learningContent.isPublic, true),
        eq(learningContent.userId, filters.userId)
      )
    );
  } else {
    whereConditions.push(eq(learningContent.isPublic, true));
  }

  // Add filters
  if (filters.learningLevel) {
    whereConditions.push(eq(learningContent.learningLevel, filters.learningLevel));
  }

  if (filters.search) {
    const searchCondition = or(
      ilike(learningContent.title, `%${filters.search}%`),
      ilike(learningContent.description, `%${filters.search}%`)
    );
    if (searchCondition) {
      whereConditions.push(searchCondition);
    }
  }

  // Build sort order
  const sortColumn = filters.sortBy === 'title' ? learningContent.title :
                    filters.sortBy === 'createdAt' ? learningContent.createdAt :
                    learningContent.updatedAt;
  const sortOrder = filters.sortOrder === 'asc' ? asc : desc;

  // Get total count
  const totalResult = await db
    .select({ count: learningContent.id })
    .from(learningContent)
    .where(and(...whereConditions));

  // Get content with pagination
  const content = await db
    .select()
    .from(learningContent)
    .where(and(...whereConditions))
    .orderBy(sortOrder(sortColumn))
    .limit(filters.limit || 20)
    .offset(filters.offset || 0);

  return {
    content,
    total: totalResult.length,
    hasMore: content.length === (filters.limit || 20),
  };
}

/**
 * Update learning content
 * @param db - Database connection
 * @param contentId - Content ID to update
 * @param userId - User ID for ownership verification
 * @param updates - Partial updates to apply
 * @returns Promise<LearningContent> - The updated content with full type safety
 */
export async function updateLearningContent(
  db: Database,
  contentId: string,
  userId: string,
  updates: Partial<Omit<LearningContent, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<LearningContent> {
  // Verify ownership
  const existing = await db
    .select()
    .from(learningContent)
    .where(
      and(
        eq(learningContent.id, contentId),
        eq(learningContent.userId, userId)
      )
    )
    .limit(1);

  if (!existing.length) {
    throw new Error('Learning content not found or access denied');
  }

  // Update the content and return it in a single operation for better type safety
  const [updatedContent] = await db
    .update(learningContent)
    .set({
      ...updates,
      updatedAt: new Date(),
    })
    .where(eq(learningContent.id, contentId))
    .returning();

  return updatedContent;
}

/**
 * Delete learning content
 * @param db - Database connection
 * @param contentId - Content ID to delete
 * @param userId - User ID for ownership verification
 * @returns Promise<boolean> - True if deletion was successful
 */
export async function deleteLearningContent(
  db: Database,
  contentId: string,
  userId: string
): Promise<boolean> {
  // Verify ownership
  const existing = await db
    .select()
    .from(learningContent)
    .where(
      and(
        eq(learningContent.id, contentId),
        eq(learningContent.userId, userId)
      )
    )
    .limit(1);

  if (!existing.length) {
    throw new Error('Learning content not found or access denied');
  }

  // Delete the content
  await db
    .delete(learningContent)
    .where(eq(learningContent.id, contentId));

  return true;
}

/**
 * Duplicate learning content
 * @param db - Database connection
 * @param contentId - Content ID to duplicate
 * @param userId - User ID for the new content owner
 * @param newTitle - Optional new title for the duplicate
 * @returns Promise<LearningContent> - The duplicated content with full type safety
 */
export async function duplicateLearningContent(
  db: Database,
  contentId: string,
  userId: string,
  newTitle?: string
): Promise<LearningContent> {
  // Get the original content
  const original = await getLearningContentById(db, contentId, userId);

  if (!original) {
    throw new Error('Learning content not found or access denied');
  }

  // Create a duplicate
  const duplicateData: Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt'> = {
    title: newTitle || `${original.title} (Copy)`,
    description: original.description,
    steps: original.steps,
    learningLevel: original.learningLevel,
    estimatedReadingTime: original.estimatedReadingTime,
    isPublic: false, // Duplicates are private by default
    tags: original.tags,
    aiMetadata: original.aiMetadata,
    userId,
  };

  return await saveLearningContent(db, duplicateData);
}

/**
 * Get learning content statistics for a user
 * @param db - Database connection
 * @param userId - User ID to get stats for
 * @returns Promise<LearningContentStatsResponse> - User's content statistics with full type safety
 */
export async function getLearningContentStats(
  db: Database,
  userId: string
): Promise<LearningContentStatsResponse> {
  const userContent = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.userId, userId));

  const stats: LearningContentStatsResponse = {
    totalContent: userContent.length,
    publicContent: userContent.filter(c => c.isPublic).length,
    privateContent: userContent.filter(c => !c.isPublic).length,
    contentByLevel: {
      beginner: userContent.filter(c => c.learningLevel === 'beginner').length,
      intermediate: userContent.filter(c => c.learningLevel === 'intermediate').length,
      advanced: userContent.filter(c => c.learningLevel === 'advanced').length,
    },
  };

  return stats;
}

/**
 * Get user's learning content with progress data
 * Joins learningContent with learningProgress to provide comprehensive view
 * @param db - Database connection
 * @param userId - User ID to filter by
 * @param filters - Optional filters for content
 * @returns Promise<{ content: LearningContentWithProgress[]; total: number; hasMore: boolean }>
 */
export async function getMyLearningContentWithProgress(
  db: Database,
  userId: string,
  filters: MyLearningFilters = {}
): Promise<{ content: LearningContentWithProgress[]; total: number; hasMore: boolean }> {
  const whereConditions = [eq(learningContent.userId, userId)];

  // Add filters
  if (filters.learningLevel) {
    whereConditions.push(eq(learningContent.learningLevel, filters.learningLevel));
  }

  if (filters.contentType) {
    whereConditions.push(eq(learningContent.contentType, filters.contentType));
  }

  if (filters.isPublic !== undefined) {
    whereConditions.push(eq(learningContent.isPublic, filters.isPublic));
  }

  if (filters.search) {
    const searchCondition = or(
      ilike(learningContent.title, `%${filters.search}%`),
      ilike(learningContent.description, `%${filters.search}%`)
    );
    if (searchCondition) {
      whereConditions.push(searchCondition);
    }
  }

  if (filters.tags && filters.tags.length > 0) {
    // For JSON array contains, we need to check if any of the filter tags exist in the content tags
    const tagConditions = filters.tags.map(tag =>
      like(learningContent.tags, `%"${tag}"%`)
    );
    whereConditions.push(or(...tagConditions));
  }

  if (filters.dateRange) {
    if (filters.dateRange.from) {
      whereConditions.push(gte(learningContent.createdAt, filters.dateRange.from));
    }
    if (filters.dateRange.to) {
      whereConditions.push(lte(learningContent.createdAt, filters.dateRange.to));
    }
  }

  if (filters.readingTimeRange) {
    if (filters.readingTimeRange.min) {
      whereConditions.push(gte(learningContent.estimatedReadingTime, filters.readingTimeRange.min));
    }
    if (filters.readingTimeRange.max) {
      whereConditions.push(lte(learningContent.estimatedReadingTime, filters.readingTimeRange.max));
    }
  }

  // Determine sort order
  const sortBy = filters.sortBy || 'updatedAt';
  const sortOrder = filters.sortOrder || 'desc';

  let orderByClause;
  switch (sortBy) {
    case 'title':
      orderByClause = sortOrder === 'asc' ? asc(learningContent.title) : desc(learningContent.title);
      break;
    case 'createdAt':
      orderByClause = sortOrder === 'asc' ? asc(learningContent.createdAt) : desc(learningContent.createdAt);
      break;
    case 'progress':
      orderByClause = sortOrder === 'asc' ? asc(learningProgress.completionPercentage) : desc(learningProgress.completionPercentage);
      break;
    default:
      orderByClause = sortOrder === 'asc' ? asc(learningContent.updatedAt) : desc(learningContent.updatedAt);
  }

  // Get total count for pagination
  const myLearningTotalResult = await db
    .select()
    .from(learningContent)
    .where(and(...whereConditions));

  // Get content with progress data using LEFT JOIN
  const content = await db
    .select({
      // Learning content fields
      id: learningContent.id,
      title: learningContent.title,
      description: learningContent.description,
      steps: learningContent.steps,
      learningLevel: learningContent.learningLevel,
      estimatedReadingTime: learningContent.estimatedReadingTime,
      contentType: learningContent.contentType,
      isPublic: learningContent.isPublic,
      tags: learningContent.tags,
      aiMetadata: learningContent.aiMetadata,
      userId: learningContent.userId,
      recommendationId: learningContent.recommendationId,
      createdAt: learningContent.createdAt,
      updatedAt: learningContent.updatedAt,
      // Progress fields (nullable)
      progressId: learningProgress.id,
      currentStepIndex: learningProgress.currentStepIndex,
      completedSteps: learningProgress.completedSteps,
      totalTimeSpent: learningProgress.totalTimeSpent,
      completionPercentage: learningProgress.completionPercentage,
      isCompleted: learningProgress.isCompleted,
      lastAccessedAt: learningProgress.lastAccessedAt,
      sessionCount: learningProgress.sessionCount,
    })
    .from(learningContent)
    .leftJoin(
      learningProgress,
      and(
        eq(learningProgress.contentId, learningContent.id),
        eq(learningProgress.userId, userId)
      )
    )
    .where(and(...whereConditions))
    .orderBy(orderByClause)
    .limit(filters.limit || 20)
    .offset(filters.offset || 0);

  // Apply completion status filter after the join
  let filteredContent = content;
  if (filters.completionStatus) {
    filteredContent = content.filter(item => {
      switch (filters.completionStatus) {
        case 'not-started':
          return !item.progressId || item.completionPercentage === 0;
        case 'in-progress':
          return item.progressId && item.completionPercentage > 0 && !item.isCompleted;
        case 'completed':
          return item.isCompleted === true;
        default:
          return true;
      }
    });
  }

  return {
    content: filteredContent as LearningContentWithProgress[],
    total: myLearningTotalResult.length,
    hasMore: filteredContent.length === (filters.limit || 20),
  };
}

/**
 * Toggle the public status of learning content
 * @param db - Database connection
 * @param contentId - Content ID to toggle
 * @param userId - User ID for ownership verification
 * @returns Promise<LearningContent> - Updated content
 */
export async function toggleLearningContentPublic(
  db: Database,
  contentId: string,
  userId: string
): Promise<LearningContent> {
  // First verify ownership
  const existing = await db
    .select()
    .from(learningContent)
    .where(and(
      eq(learningContent.id, contentId),
      eq(learningContent.userId, userId)
    ))
    .limit(1);

  if (!existing.length) {
    throw new Error('Content not found or access denied');
  }

  const currentContent = existing[0];
  const newPublicStatus = !currentContent.isPublic;

  // Update the public status
  await db
    .update(learningContent)
    .set({
      isPublic: newPublicStatus,
      updatedAt: new Date(),
    })
    .where(and(
      eq(learningContent.id, contentId),
      eq(learningContent.userId, userId)
    ));

  // Return updated content
  const updated = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.id, contentId))
    .limit(1);

  return updated[0];
}
