/**
 * Quiz Generation Service
 * Generates quiz questions from learning content using AI
 */

import { generateObject } from 'ai';
import { createA<PERSON>rovider, selectOptimalModel, AI_CONFIG } from '../config/ai-config';
import { 
  aiQuizGenerationResponseSchema,
  quizQuestionSchema,
  type QuizType,
  type QuizDifficulty
} from '../schemas/quiz-generation';
import {
  QUIZ_GENERATION_SYSTEM_PROMPT,
  createQuizGenerationPromptWithQuotas
} from '../prompts/quiz-generation';
import type { LearningContent } from '~/db/schema/learning-content';
import type { QuizGenerationConfig } from '~/components/quiz/types';

// Quiz generation options
interface QuizGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  validateContent?: boolean;
  retryOnFailure?: boolean;
  preferCostEffective?: boolean;
  env?: Record<string, any>;
}

// Generated quiz response interface
export interface GeneratedQuiz {
  id: string;
  title: string;
  description: string;
  learningContentId: string;
  questions: any[];
  estimatedDuration: number;
  totalPoints: number;
  difficulty: QuizDifficulty;
  metadata: {
    generatedAt: string;
    aiModel: string;
    sourceStepsUsed: string[];
    difficultyDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
  };
  isPublic: boolean;
  allowRetakes: boolean;
  showCorrectAnswers: boolean;
  shuffleQuestions: boolean;
  timeLimit?: number;
}

/**
 * Extract text content from a learning step block
 */
function extractTextFromBlock(block: any): string {
  const { type, data } = block;

  switch (type) {
    case 'paragraph':
      if (typeof data === 'string') {
        return data;
      } else if (Array.isArray(data)) {
        return data.join('\n');
      } else if (data && typeof data.text === 'string') {
        return data.text;
      } else if (data && Array.isArray(data.text)) {
        return data.text.join('\n');
      }
      return '';

    case 'infoBox':
      const infoData = data.title ? data : (data.data || data);
      const heading = infoData.title || infoData.heading ? `${infoData.title || infoData.heading}\n` : '';
      const content = infoData.content || '';
      const lines = Array.isArray(infoData.lines) ? infoData.lines.join('\n') : '';
      return heading + content + lines;

    case 'bulletList':
    case 'numberedList':
      if (Array.isArray(data)) {
        return data.join('\n');
      } else if (data && Array.isArray(data.items)) {
        return data.items.join('\n');
      }
      return '';

    case 'grid':
      const gridData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(gridData)) {
        return gridData.map((item: any) => `${item.title}: ${item.content}`).join('\n');
      }
      return '';

    case 'comparison':
      const comparisonData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(comparisonData)) {
        return comparisonData.map((item: any) =>
          `${item.label}: Before - ${item.before}, After - ${item.after}`
        ).join('\n');
      }
      return '';

    case 'table':
      const tableData = data.headers ? data : (data.data || data);
      if (tableData.headers && tableData.rows) {
        const headerRow = tableData.headers.join(' | ');
        const dataRows = tableData.rows.map((row: any[]) => row.join(' | ')).join('\n');
        return `${headerRow}\n${dataRows}`;
      }
      return '';

    case 'keyValueGrid':
      const kvData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(kvData)) {
        return kvData.map((item: any) => `${item.key}: ${item.value}`).join('\n');
      }
      return '';

    case 'scatterPlot':
      const plotData = data.data ? data : (data.config || data);
      if (plotData.data && Array.isArray(plotData.data)) {
        return plotData.data.map((point: any) =>
          `${point.label}: (${point.x}, ${point.y})`
        ).join('\n');
      }
      return '';

    default:
      return '';
  }
}

/**
 * Extract all text content from a learning step
 */
function extractStepContent(step: any): string {
  const title = step.title;
  const blockContents = step.blocks
    .map((block: any) => extractTextFromBlock(block))
    .filter((content: string) => content.trim().length > 0);

  return [title, ...blockContents].join('\n\n');
}

/**
 * Create question type quotas based on configuration
 */
function createQuestionTypeQuotas(quizTypes: QuizType[], questionsPerType: number): Map<QuizType, number> {
  const quotas = new Map<QuizType, number>();
  quizTypes.forEach(type => {
    quotas.set(type, questionsPerType);
  });
  return quotas;
}

/**
 * Distribute questions across content steps
 */
function distributeQuestionsAcrossSteps(
  totalQuestions: number,
  numberOfSteps: number,
  quizTypes: QuizType[],
  questionsPerType: number
): Array<{
  questionsToGenerate: number;
  typesToUse: QuizType[];
  typeQuotas: Map<QuizType, number>;
}> {
  const distribution = [];
  const questionsPerStep = Math.ceil(totalQuestions / numberOfSteps);
  
  for (let i = 0; i < numberOfSteps; i++) {
    const quotas = createQuestionTypeQuotas(quizTypes, Math.ceil(questionsPerType / numberOfSteps));
    distribution.push({
      questionsToGenerate: Math.min(questionsPerStep, totalQuestions - i * questionsPerStep),
      typesToUse: quizTypes,
      typeQuotas: quotas
    });
  }
  
  return distribution;
}

/**
 * Transform AI-generated question to match strict schema requirements
 */
function transformAIQuestionToStrict(rawQuestion: any, stepId: string, stepContent: string): any | null {
  try {
    const baseQuestion = {
      id: crypto.randomUUID(),
      type: rawQuestion.type,
      difficulty: rawQuestion.difficulty || 'medium',
      sourceStepId: stepId,
      sourceContent: stepContent.substring(0, 500),
      points: rawQuestion.points || 1,
    };

    // Transform based on question type
    switch (rawQuestion.type) {
      case 'flashcard':
        if (!rawQuestion.front || !rawQuestion.back) {
          console.warn(`Flashcard question missing required fields`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'flashcard',
          front: rawQuestion.front,
          back: rawQuestion.back,
          hint: rawQuestion.hint,
        };

      case 'multipleChoice':
        if (!rawQuestion.question || !rawQuestion.options || rawQuestion.options.length !== 4 || rawQuestion.correctAnswerIndex === undefined) {
          console.warn(`Multiple choice question missing required fields`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'multipleChoice',
          question: rawQuestion.question,
          options: rawQuestion.options,
          correctAnswerIndex: rawQuestion.correctAnswerIndex,
          explanation: rawQuestion.explanation,
        };

      case 'trueFalse':
        if (!rawQuestion.statement || rawQuestion.correctAnswer === undefined) {
          console.warn(`True/false question missing required fields`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'trueFalse',
          statement: rawQuestion.statement,
          correctAnswer: rawQuestion.correctAnswer,
          explanation: rawQuestion.explanation,
        };

      case 'fillInBlank':
        let blanks = rawQuestion.blanks;
        if (!rawQuestion.text || !blanks || blanks.length === 0) {
          console.warn(`Fill in blank question missing required fields`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'fillInBlank',
          text: rawQuestion.text,
          blanks: blanks,
          hint: rawQuestion.hint,
        };

      case 'matching':
        let pairs = rawQuestion.pairs;
        if (!pairs || pairs.length < 3) {
          console.warn(`Matching question missing required pairs`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'matching',
          instruction: rawQuestion.instruction || 'Match the following items:',
          pairs: pairs,
        };

      case 'freeText':
        const question = rawQuestion.question;
        const answerType = rawQuestion.answerType || 'short';
        const maxLength = rawQuestion.maxLength || (answerType === 'short' ? 200 : 500);
        const sampleAnswer = rawQuestion.sampleAnswer || 'A comprehensive answer based on the provided content.';
        let evaluationCriteria = rawQuestion.evaluationCriteria || ['Demonstrates understanding of key concepts'];

        if (!question) {
          console.warn(`Free text question missing required question field`);
          return null;
        }

        return {
          ...baseQuestion,
          type: 'freeText',
          question: question,
          answerType: answerType,
          maxLength: maxLength,
          sampleAnswer: sampleAnswer,
          evaluationCriteria: evaluationCriteria,
        };

      case 'ordering':
        if (!rawQuestion.items || !rawQuestion.correctOrder || rawQuestion.items.length < 3) {
          console.warn(`Ordering question missing required fields`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'ordering',
          instruction: rawQuestion.instruction || 'Arrange the following items in the correct order:',
          items: rawQuestion.items,
          correctOrder: rawQuestion.correctOrder,
          orderType: rawQuestion.orderType || 'logical',
        };

      default:
        console.warn(`Unknown question type: ${rawQuestion.type}`);
        return null;
    }
  } catch (error) {
    console.warn(`Failed to transform AI question:`, error);
    return null;
  }
}

/**
 * Generate questions for a specific step
 */
async function generateQuestionsForStep(
  step: any,
  typeQuotas: Map<QuizType, number>,
  difficulty: QuizDifficulty,
  options: QuizGenerationOptions
): Promise<any[]> {
  const stepContent = extractStepContent(step);
  if (stepContent.trim().length < 50) {
    console.warn(`Step ${step.id} has insufficient content for quiz generation`);
    return [];
  }

  const openrouter = createAIProvider(options.env);
  const primaryModel = options.model || selectOptimalModel(['paragraph'], 'intermediate', options.preferCostEffective);

  const userPrompt = createQuizGenerationPromptWithQuotas(
    stepContent,
    step.title,
    step.id,
    typeQuotas,
    difficulty
  );

  try {
    console.info(`Generating questions for step ${step.id} with model: ${primaryModel}`);
    const currentModel = openrouter(primaryModel);

    const result = await generateObject({
      model: currentModel,
      schema: aiQuizGenerationResponseSchema,
      system: QUIZ_GENERATION_SYSTEM_PROMPT,
      prompt: userPrompt,
      temperature: options.temperature || 0.3,
      maxTokens: options.maxTokens || 4000,
    });

    const aiResponse = result.object;
    console.info(`Generated ${aiResponse.questions?.length || 0} questions for step ${step.id}`);

    // Transform and validate each question
    const validQuestions = [];
    for (const rawQuestion of aiResponse.questions || []) {
      const transformedQuestion = transformAIQuestionToStrict(rawQuestion, step.id, stepContent);
      if (transformedQuestion) {
        validQuestions.push(transformedQuestion);
      }
    }

    console.info(`${validQuestions.length} valid questions after transformation for step ${step.id}`);
    return validQuestions;

  } catch (error) {
    console.error(`Question generation failed for step ${step.id}:`, error);
    return [];
  }
}

/**
 * Main function to generate quiz from learning content
 */
export async function generateQuizFromContent(
  learningContent: LearningContent,
  config: QuizGenerationConfig,
  options: QuizGenerationOptions = {}
): Promise<GeneratedQuiz> {
  if (learningContent.steps.length === 0) {
    throw new Error('Learning content must have at least one step');
  }

  const totalTargetQuestions = config.quizTypes.length * config.questionsPerType;
  console.info(`Quiz generation strategy:`, {
    totalSteps: learningContent.steps.length,
    targetQuestions: totalTargetQuestions,
    quizTypes: config.quizTypes,
    questionsPerType: config.questionsPerType
  });

  // Distribute questions across steps
  const questionDistribution = distributeQuestionsAcrossSteps(
    totalTargetQuestions,
    learningContent.steps.length,
    config.quizTypes,
    config.questionsPerType
  );

  const allQuestions: any[] = [];
  const sourceStepsUsed: string[] = [];

  // Generate questions for each step
  for (let i = 0; i < learningContent.steps.length; i++) {
    const step = learningContent.steps[i];
    const distribution = questionDistribution[i];

    if (distribution.questionsToGenerate === 0) {
      console.info(`Skipping step ${step.id} - no questions allocated`);
      continue;
    }

    const stepQuestions = await generateQuestionsForStep(
      step,
      distribution.typeQuotas,
      config.difficulty,
      options
    );

    if (stepQuestions.length > 0) {
      allQuestions.push(...stepQuestions);
      sourceStepsUsed.push(step.id);
    }
  }

  // Calculate difficulty distribution
  const difficultyDistribution: Record<string, number> = {};
  const typeDistribution: Record<string, number> = {};

  allQuestions.forEach(q => {
    difficultyDistribution[q.difficulty] = (difficultyDistribution[q.difficulty] || 0) + 1;
    typeDistribution[q.type] = (typeDistribution[q.type] || 0) + 1;
  });

  // Calculate estimated duration (1.5 minutes per question on average)
  const estimatedDuration = Math.ceil(allQuestions.length * 1.5);
  const totalPoints = allQuestions.reduce((sum, q) => sum + (q.points || 1), 0);

  const generatedQuiz: GeneratedQuiz = {
    id: crypto.randomUUID(),
    title: `Quiz: ${learningContent.title}`,
    description: `Generated quiz based on: ${learningContent.description || learningContent.title}`,
    learningContentId: learningContent.id,
    questions: allQuestions,
    estimatedDuration,
    totalPoints,
    difficulty: config.difficulty,
    metadata: {
      generatedAt: new Date().toISOString(),
      aiModel: options.model || selectOptimalModel(['paragraph'], 'intermediate', options.preferCostEffective),
      sourceStepsUsed,
      difficultyDistribution,
      typeDistribution,
    },
    isPublic: false,
    allowRetakes: true,
    showCorrectAnswers: config.includeExplanations,
    shuffleQuestions: config.shuffleQuestions,
    timeLimit: config.timeLimit,
  };

  console.info(`Quiz generation completed:`, {
    questionsGenerated: allQuestions.length,
    targetQuestions: totalTargetQuestions,
    estimatedDuration,
    totalPoints,
    typeDistribution,
    difficultyDistribution
  });

  return generatedQuiz;
}