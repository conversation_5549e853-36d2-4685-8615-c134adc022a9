import React from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardHeader } from "~/components/ui/card";
import {
  FileQuestion,
  BookmarkPlus,
  Star,
  Share2,
  <PERSON><PERSON><PERSON>,
  Plus,
  Clock,
  User,
  Brain,
} from "lucide-react";
import { cn } from "~/lib/utils";

interface LearningContentHeaderProps {
  title: string;
  description: string;
  progress: number; // 0-100
  currentStep: number;
  totalSteps: number;
  estimatedReadingTime: number;
  learningLevel: "beginner" | "intermediate" | "advanced";
  isPublic: boolean;
  onGenerateQuiz?: () => void;
  onNotesBookmarks?: () => void;
  onRateFeedback?: () => void;
  onShare?: () => void;
  onGenerateSimilar?: () => void;
  onCreateNew?: () => void;
  className?: string;
}

export function LearningContentHeader({
  title,
  description,
  progress,
  currentStep,
  totalSteps,
  estimatedReadingTime,
  learningLevel,
  isPublic,
  onGenerateQuiz,
  onNotesBookmarks,
  onRateFeedback,
  onShare,
  onGenerateSimilar,
  onCreateNew,
  className,
}: LearningContentHeaderProps) {
  const getLevelColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700";
      case "intermediate":
        return "bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700";
      case "advanced":
        return "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700";
      default:
        return "bg-gray-50 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700";
    }
  };

  return (
    <Card
      className={cn(
        "border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg dark:shadow-black/20 rounded-lg",
        className
      )}
    >
      <CardHeader className="pb-6">
        {/* Title and Description */}
        <div className="space-y-3">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {title}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
            {description}
          </p>
        </div>

        {/* Progress and Metadata */}
        <div className="flex items-center justify-between mt-6">
          {/* Metadata Row */}
          <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-300">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              <span>{estimatedReadingTime} min read</span>
            </div>
            <div className="flex items-center">
              <FileQuestion className="h-4 w-4 mr-2" />
              <span>{totalSteps} steps</span>
            </div>
            <div className="flex items-center">
              <Share2 className="h-4 w-4 mr-2" />
              <span>{isPublic ? "Public" : "Private"}</span>
            </div>
          </div>

          {/* Progress Info */}
          <div className="flex items-center space-x-4">
                  {/* Level Indicator */}
            <div className="flex items-center justify-between">
              <Badge
                className={`${getLevelColor(learningLevel)} px-3 py-1.5 font-medium`}
              >
                {learningLevel}
              </Badge>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Step{" "}
              <span className="font-semibold text-gray-900 dark:text-gray-100">
                {currentStep + 1}
              </span>
              /<span className="font-semibold">{totalSteps}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {Math.round(progress)}%
              </div>
              <div className="w-12 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Action Buttons */}
        <div className="flex items-center space-x-2 flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onGenerateQuiz}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <FileQuestion className="h-4 w-4" />
            <span>Generate Quiz</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onNotesBookmarks}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <BookmarkPlus className="h-4 w-4" />
            <span>Notes & Bookmarks</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onRateFeedback}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Star className="h-4 w-4" />
            <span>Rate & Feedback</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onShare}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Share2 className="h-4 w-4" />
            <span>Share</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onGenerateSimilar}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Sparkles className="h-4 w-4" />
            <span>Generate Similar</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCreateNew}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Create New</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
