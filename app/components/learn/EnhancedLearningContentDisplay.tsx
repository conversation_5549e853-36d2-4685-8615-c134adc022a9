import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import {
  Clock,
  Trophy,
  FileQuestion,
  Eye,
  RotateCcw,
  MoreVertical,
  Loader2,
  Bookmark,
  StickyNote,
  Check,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { cn } from "~/lib/utils";
import { LearningContentHeader } from "./LearningContentHeader";
import { AskQuestionsSidebar } from "./AskQuestionsSidebar";
import { MultiStepExplain } from "../templates/MultiStepExplain";
import type { StepConfig } from "../templates/types";

// Types for quiz history and progress data
interface QuizHistoryItem {
  id: string;
  quizId: string;
  quizTitle?: string;
  isCompleted: boolean;
  score?: {
    percentage?: number;
  };
  startedAt: string;
}

interface ProgressData {
  progress?: {
    completedSteps: number[];
    isCompleted: boolean;
    sessionCountComplete?: number;
    bookmarks: Array<{ stepIndex: number }>;
    notes: Array<{ stepIndex: number; content: string }>;
  };
}

interface EnhancedLearningContentDisplayProps {
  contentId: string;
  title: string;
  description: string;
  steps: StepConfig[];
  learningLevel: "beginner" | "intermediate" | "advanced";
  estimatedReadingTime: number;
  isPublic: boolean;
  initialStep?: number;
  completedSteps?: number[];
  progress?: number;
  onStepChange?: (step: number, isLessonComplete?: boolean) => void;
  onProgressUpdate?: (progress: number) => void;
  onQuizSelect?: (quizId: string, attemptId?: string) => void;
  onRetakeQuiz?: (quizId: string) => void;
  onGenerateNew?: () => void;
  className?: string;
  totalTimeSpent?: number;
  sessionCount?: number;
}

export function EnhancedLearningContentDisplay({
  contentId,
  title,
  description,
  steps,
  learningLevel,
  estimatedReadingTime,
  isPublic,
  initialStep = 0,
  completedSteps = [],
  progress = 0,
  onStepChange,
  onProgressUpdate,
  onQuizSelect,
  onRetakeQuiz,
  onGenerateNew,
  className,
  totalTimeSpent = 0,
  sessionCount = 0,
}: EnhancedLearningContentDisplayProps) {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [timeSpent, setTimeSpent] = useState(0);
  const [stepStartTime, setStepStartTime] = useState<number>(Date.now());
  const [isLessonCompleted, setIsLessonCompleted] = useState(false);

  // State for note dialog
  const [showNoteDialog, setShowNoteDialog] = useState(false);
  const [noteContent, setNoteContent] = useState("");
  const [editingNoteIndex, setEditingNoteIndex] = useState<number | null>(null);

  // Trophy animation state
  const [trophyAnimations, setTrophyAnimations] = useState<Set<number>>(
    new Set()
  );
  const previousCompletedStepsRef = useRef<number[]>([]);

  // Mock progress data - TODO: Replace with actual API calls
  const progressData: ProgressData = {
    progress: {
      completedSteps: completedSteps,
      isCompleted: false,
      sessionCountComplete: sessionCount,
      bookmarks: [], // TODO: Implement bookmarks
      notes: [], // TODO: Implement notes
    },
  };

  // State for pagination
  const [quizHistoryOffset, setQuizHistoryOffset] = useState(0);
  const [allQuizAttempts, setAllQuizAttempts] = useState<QuizHistoryItem[]>([]);
  const [hasMoreQuizzes, setHasMoreQuizzes] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Mock quiz history data - TODO: Replace with actual API calls
  const quizHistoryData = {
    attempts: allQuizAttempts,
    hasMore: hasMoreQuizzes,
    stats: {
      totalAttempts: 0,
      completedAttempts: 0,
      averageScore: 0,
      bestScore: 0,
    },
  };

  useEffect(() => {
    setCurrentStep(initialStep);
  }, [initialStep]);

  // Initialize previousCompletedStepsRef with existing progress data
  useEffect(() => {
    if (progressData?.progress?.completedSteps) {
      previousCompletedStepsRef.current = [
        ...progressData.progress.completedSteps,
      ];
    }
  }, [progressData?.progress?.completedSteps]);

  // Track time spent with optimized interval
  useEffect(() => {
    // Don't track time in review mode or if lesson is already completed
    if (progressData?.progress?.isCompleted) {
      return;
    }

    // Use a more efficient interval that only updates when component is visible and lesson is not completed
    const updateTime = () => {
      if (document.visibilityState === "visible" && !isLessonCompleted) {
        const sessionTime = Math.floor((Date.now() - stepStartTime) / 1000);
        setTimeSpent(sessionTime);
      }
    };

    const intervalId = setInterval(updateTime, 1000);

    // Also update when page becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !isLessonCompleted) {
        updateTime();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      clearInterval(intervalId);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isLessonCompleted, stepStartTime, progressData?.progress?.isCompleted]);

  // Trophy animation logic with cleanup
  const triggerTrophyAnimation = useCallback((stepIndex: number) => {
    setTrophyAnimations((prev) => new Set([...prev, stepIndex]));

    const timeoutId = setTimeout(() => {
      setTrophyAnimations((prev) => {
        const newSet = new Set(prev);
        newSet.delete(stepIndex);
        return newSet;
      });
    }, 2000); // Animation duration

    // Store timeout ID for potential cleanup
    return () => clearTimeout(timeoutId);
  }, []);

  // Handle step changes with improved error handling
  const handleStepChange = useCallback(
    async (step: number, isLessonComplete = false) => {
      // Validate step bounds
      if (step < 0 || step >= steps.length) {
        console.warn(
          `Invalid step index: ${step}. Must be between 0 and ${steps.length - 1}`
        );
        return;
      }

      // Calculate time spent on current step
      const currentTime = Date.now();
      const stepTimeSpent = Math.floor((currentTime - stepStartTime) / 1000);

      // Trigger trophy animation for the completed step
      if (step > currentStep && !completedSteps.includes(currentStep)) {
        // Moving to next step - animate current step as completed
        triggerTrophyAnimation(currentStep);
      } else if (isLessonComplete && !completedSteps.includes(step)) {
        // Final step completion - animate the final step
        triggerTrophyAnimation(step);
      }

      setCurrentStep(step);
      if (onStepChange) {
        onStepChange(step, isLessonComplete);
      }

      // Calculate and update progress
      const newProgress = ((step + 1) / steps.length) * 100;
      onProgressUpdate?.(newProgress);

      // TODO: Save progress to backend
      // This would typically call an API to save progress
      console.log("Saving progress:", {
        contentId,
        currentStepIndex: step,
        completedSteps: [
          ...completedSteps,
          ...(isLessonComplete ? [step] : []),
        ],
        timeSpent: stepTimeSpent,
      });

      // Update step start time for next calculation
      setStepStartTime(currentTime);
    },
    [
      currentStep,
      steps.length,
      completedSteps,
      stepStartTime,
      contentId,
      onStepChange,
      onProgressUpdate,
      triggerTrophyAnimation,
    ]
  );

  // Sync with external step changes
  useEffect(() => {
    setCurrentStep(initialStep ?? 0);
  }, [initialStep]);

  // Reset step start time when step changes
  useEffect(() => {
    setStepStartTime(Date.now());
  }, [currentStep]);

  const progress_data = progressData?.progress;

  // Check if lesson is completed when all steps are done
  useEffect(() => {
    if (progress_data?.isCompleted) {
      return;
    }

    const allStepsCompleted =
      completedSteps.length === steps.length && steps.length > 0;
    if (allStepsCompleted && !isLessonCompleted) {
      setIsLessonCompleted(true);
    }
    // Reset completion status if not all steps are completed
    if (!allStepsCompleted && isLessonCompleted) {
      setIsLessonCompleted(false);
    }
  }, [
    completedSteps.length,
    steps.length,
    isLessonCompleted,
    progress_data?.isCompleted,
  ]);

  const completionPercentage = useMemo(() => {
    if (isLessonCompleted) {
      return 100;
    }
    return Math.round((completedSteps.length / steps.length) * 100);
  }, [completedSteps.length, steps.length, isLessonCompleted]);

  const bookmarks = progress_data?.bookmarks || [];
  const notes = progress_data?.notes || [];

  // Current step bookmark and note status with memoization
  const isStepBookmarked = useMemo(() => {
    return bookmarks.some(
      (b: { stepIndex: number }) => b.stepIndex === currentStep
    );
  }, [bookmarks, currentStep]);

  const stepNotes = useMemo(() => {
    return notes.filter(
      (n: { stepIndex: number }) => n.stepIndex === currentStep
    );
  }, [notes, currentStep]);

  // Format time
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      handleStepChange(currentStep - 1);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      handleStepChange(currentStep + 1);
    }
  };

  // Restart lesson functionality
  const handleRestartLesson = useCallback(async () => {
    try {
      // Reset all states
      setCurrentStep(0);
      setTimeSpent(0);
      setStepStartTime(Date.now());
      setIsLessonCompleted(false);
      setTrophyAnimations(new Set());

      // TODO: Reset progress in database
      console.log("Restarting lesson for content:", contentId);

      // Call external onStepChange if provided
      if (onStepChange) {
        onStepChange(0);
      }
    } catch (error) {
      console.error("Failed to restart lesson:", error);
    }
  }, [contentId, onStepChange]);

  // Bookmark toggle handler
  const handleToggleBookmark = useCallback(async () => {
    // Prevent bookmark changes in review mode
    if (progress_data?.isCompleted) {
      return;
    }

    try {
      // TODO: Implement bookmark API calls
      console.log("Toggle bookmark for step:", currentStep);
    } catch (error) {
      console.error("Failed to toggle bookmark:", error);
    }
  }, [isStepBookmarked, contentId, currentStep, progress_data?.isCompleted]);

  // Note handlers
  const handleAddNote = () => {
    // Prevent note changes in review mode
    if (progress_data?.isCompleted) {
      return;
    }

    setShowNoteDialog(true);
    setNoteContent("");
    setEditingNoteIndex(null);
  };

  const handleSaveNote = async () => {
    if (!noteContent.trim()) return;

    // Prevent note changes in review mode
    if (progress_data?.isCompleted) {
      return;
    }

    try {
      // TODO: Implement note API calls
      console.log("Save note for step:", currentStep, "content:", noteContent);

      setShowNoteDialog(false);
      setNoteContent("");
      setEditingNoteIndex(null);
    } catch (error) {
      console.error("Failed to save note:", error);
    }
  };

  // Load more quiz attempts
  const handleLoadMore = async () => {
    if (isLoadingMore || !hasMoreQuizzes) return;

    setIsLoadingMore(true);
    const newOffset = quizHistoryOffset + 3;

    try {
      // TODO: Implement quiz history API call
      console.log("Load more quiz attempts, offset:", newOffset);
      setQuizHistoryOffset(newOffset);
    } catch (error) {
      console.error("Failed to load more quiz attempts:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const handleGenerateQuiz = () => {
    onGenerateNew?.();
  };

  const handleNotesBookmarks = () => {
    // TODO: Implement notes and bookmarks
    console.log("Open notes and bookmarks for content:", contentId);
  };

  const handleRateFeedback = () => {
    // TODO: Implement rating and feedback
    console.log("Open rating and feedback for content:", contentId);
  };

  const handleShare = () => {
    // TODO: Implement sharing
    console.log("Share content:", contentId);
  };

  const handleGenerateSimilar = () => {
    // TODO: Implement generate similar content
    console.log("Generate similar content to:", contentId);
  };

  const handleCreateNew = () => {
    // TODO: Navigate to create new content
    console.log("Create new content");
  };

  const handleSendMessage = async (message: string): Promise<string> => {
    // TODO: Implement AI chat functionality
    // This would typically call an API to get AI responses
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(
          `I understand you're asking about "${message}". Let me help you with that aspect of ${title}. Could you be more specific about what you'd like to know?`
        );
      }, 1000);
    });
  };

  const currentStepData = steps[currentStep];
  const quizHistory = allQuizAttempts;
  const stats = quizHistoryData?.stats || {
    totalAttempts: 0,
    completedAttempts: 0,
    averageScore: 0,
    bestScore: 0,
  };

  if (!steps || steps.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800">
        <div className="text-center text-gray-500 dark:text-gray-400">
          No steps provided
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "bg-background flex flex-col overflow-auto h-full",
        className
      )}
    >
      <div className="w-full h-full flex flex-col overflow-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
          {/* Main Content Area */}
          <div className="lg:col-span-3 flex flex-col h-full">
            {/* Header - Fixed */}
            <div className="flex-shrink-0">
              <LearningContentHeader
                title={title}
                description={description}
                progress={progress}
                currentStep={currentStep}
                totalSteps={steps.length}
                estimatedReadingTime={estimatedReadingTime}
                learningLevel={learningLevel}
                isPublic={isPublic}
                onGenerateQuiz={handleGenerateQuiz}
                onNotesBookmarks={handleNotesBookmarks}
                onRateFeedback={handleRateFeedback}
                onShare={handleShare}
                onGenerateSimilar={handleGenerateSimilar}
                onCreateNew={handleCreateNew}
              />
            </div>

            {/* Main Content Card with Three-Section Layout */}
            <div className="flex-1 min-h-0 mt-6">
              <Card className="h-full flex flex-col border-2 border-gray-200 dark:border-gray-700 shadow-xl dark:shadow-2xl dark:shadow-black/20 rounded-xl bg-white dark:bg-gray-800 transition-all duration-200">
                <CardContent className="p-0 h-full flex flex-col">
                  {/* Header Section - Enhanced Progress Indicator */}
                  <div className="flex items-center justify-between p-4 pb-3 flex-shrink-0 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/30">
                    {/* Left: Learning Progress */}
                    <div
                      className={cn(
                        "flex items-center space-x-3 text-xs text-gray-600 dark:text-gray-300"
                      )}
                    >
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>
                          {formatTime(
                            progress_data?.isCompleted
                              ? totalTimeSpent
                              : timeSpent
                          )}
                        </span>
                        {progress_data?.isCompleted && (
                          <span className="text-gray-500 dark:text-gray-400">
                            (Total)
                          </span>
                        )}
                      </div>
                      {progress_data?.isCompleted && (
                        <div className="flex items-center space-x-1">
                          <span>
                            Sessions: {progress_data?.sessionCountComplete || 0}
                          </span>
                        </div>
                      )}
                      <div
                        className={cn(
                          "font-medium",
                          isLessonCompleted
                            ? "text-green-600 dark:text-green-400"
                            : "text-blue-600 dark:text-blue-400"
                        )}
                      >
                        {completionPercentage}%
                        {isLessonCompleted && (
                          <span className="ml-1 text-green-600 dark:text-green-400">
                            ✓ Complete
                          </span>
                        )}
                      </div>
                      <div>
                        Step {currentStep + 1}/{steps.length}
                      </div>
                    </div>

                    {/* Center: Step Navigation Dots with Completion Status */}
                    <div className={cn("flex space-x-2")}>
                      {steps.map((_, index) => {
                        const isCompleted = completedSteps.includes(index);
                        const isCurrent = index === currentStep;
                        const showTrophyAnimation = trophyAnimations.has(index);
                        return (
                          <div
                            key={index}
                            className={cn(
                              "w-4 h-4 rounded-full cursor-pointer transition-all duration-300 flex items-center justify-center relative border-2 hover:scale-110 hover:shadow-lg",
                              isCompleted
                                ? "bg-green-500 dark:bg-green-400 border-green-600 dark:border-green-300 shadow-green-200 dark:shadow-green-900/50"
                                : isCurrent
                                  ? "bg-blue-500 dark:bg-blue-400 border-blue-600 dark:border-blue-300 shadow-blue-200 dark:shadow-blue-900/50 ring-2 ring-blue-300 dark:ring-blue-500"
                                  : "bg-gray-300 dark:bg-gray-600 border-gray-400 dark:border-gray-500 hover:bg-gray-400 dark:hover:bg-gray-500"
                            )}
                            onClick={() => handleStepChange(index)}
                          >
                            {isCompleted && !showTrophyAnimation && (
                              <Check className="h-2.5 w-2.5 text-white font-bold" />
                            )}
                            {showTrophyAnimation && (
                              <div className="absolute -top-0 left-1/2 transform -translate-x-1/2 z-10">
                                <Trophy className="h-4 w-4 text-yellow-400 animate-trophy-jump drop-shadow-lg" />
                                <div className="absolute inset-0 rounded-full bg-yellow-400 opacity-20 animate-ping scale-200" />
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>

                    {/* Right: Action Buttons and Quiz History */}
                    <div
                      className={cn(
                        "flex items-center space-x-3 text-xs text-gray-600 dark:text-gray-300"
                      )}
                    >
                      {/* Restart Button - only show when lesson is completed */}
                      {progress_data?.isCompleted && (
                        <button
                          onClick={handleRestartLesson}
                          className="flex items-center space-x-1 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 px-3 py-2 rounded-lg transition-all duration-200 text-green-600 dark:text-green-400 border border-green-200 dark:border-green-700 hover:border-green-300 dark:hover:border-green-600 hover:shadow-md"
                          title="Restart lesson"
                        >
                          <RotateCcw className="h-4 w-4" />
                          <span className="font-medium">Restart</span>
                        </button>
                      )}

                      {/* Bookmark Button */}
                      <button
                        onClick={handleToggleBookmark}
                        disabled={progress_data?.isCompleted}
                        className={cn(
                          "flex items-center space-x-1 px-3 py-2 rounded-lg transition-all duration-200 border",
                          progress_data?.isCompleted
                            ? "opacity-50 cursor-not-allowed border-gray-200 dark:border-gray-600"
                            : "hover:shadow-md border-gray-200 dark:border-gray-600 hover:border-yellow-300 dark:hover:border-yellow-600",
                          isStepBookmarked
                            ? "text-yellow-600 dark:text-yellow-400 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border-yellow-300 dark:border-yellow-600"
                            : "text-gray-600 dark:text-gray-400 hover:bg-gradient-to-r hover:from-yellow-50 hover:to-amber-50 dark:hover:from-yellow-900/10 dark:hover:to-amber-900/10"
                        )}
                        title={
                          progress_data?.isCompleted
                            ? "Bookmarks disabled in review mode"
                            : isStepBookmarked
                              ? "Remove bookmark"
                              : "Bookmark this step"
                        }
                      >
                        <Bookmark
                          className={cn(
                            "h-4 w-4",
                            isStepBookmarked && "fill-current"
                          )}
                        />
                      </button>

                      {/* Note Button */}
                      <button
                        onClick={handleAddNote}
                        disabled={progress_data?.isCompleted}
                        className={cn(
                          "flex items-center space-x-1 px-3 py-2 rounded-lg transition-all duration-200 relative border",
                          progress_data?.isCompleted
                            ? "opacity-50 cursor-not-allowed border-gray-200 dark:border-gray-600"
                            : "hover:shadow-md border-gray-200 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-600",
                          stepNotes.length > 0
                            ? "text-purple-600 dark:text-purple-400 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border-purple-300 dark:border-purple-600"
                            : "text-gray-600 dark:text-gray-400 hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 dark:hover:from-purple-900/10 dark:hover:to-violet-900/10"
                        )}
                        title={
                          progress_data?.isCompleted
                            ? "Notes disabled in review mode"
                            : "Add note to this step"
                        }
                      >
                        <StickyNote className="h-4 w-4" />
                        {stepNotes.length > 0 && (
                          <span className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-500 to-violet-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold border-2 border-white dark:border-gray-800 shadow-lg">
                            {stepNotes.length}
                          </span>
                        )}
                      </button>

                      {stats.completedAttempts > 0 ? (
                        <Popover key={`quiz-history-${contentId}`}>
                          <PopoverTrigger asChild>
                            <button className="flex items-center space-x-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 px-3 py-2 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md">
                              <div className="font-medium text-gray-700 dark:text-gray-300">
                                {(() => {
                                  // Calculate unique quiz count
                                  const uniqueQuizIds = new Set(
                                    quizHistory.map((quiz) => quiz.quizId)
                                  );
                                  const quizCount = uniqueQuizIds.size;
                                  return `${quizCount} quiz${quizCount !== 1 ? "zes" : ""}`;
                                })()}
                              </div>
                              <MoreVertical className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-96 p-4 border-2 border-gray-200 dark:border-gray-600 shadow-2xl dark:shadow-black/40 bg-white dark:bg-gray-800 rounded-xl"
                            align="end"
                          >
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                  Quiz History
                                </h3>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {stats.completedAttempts} attempt
                                  {stats.completedAttempts !== 1 ? "s" : ""}
                                </div>
                              </div>

                              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                                <button
                                  onClick={onGenerateNew}
                                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 text-sm bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl border border-blue-500 dark:border-blue-400"
                                >
                                  <FileQuestion className="h-4 w-4" />
                                  <span>Generate New Quiz</span>
                                </button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      ) : (
                        <button
                          onClick={onGenerateNew}
                          className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 px-3 py-2 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 hover:shadow-md font-medium"
                        >
                          <FileQuestion className="h-4 w-4" />
                          <span>Generate Quiz</span>
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Content Section - Using MultiStepExplain for proper rendering */}
                  <div className="flex-1 overflow-y-auto scrollbar-thin">
                    <MultiStepExplain
                      steps={steps}
                      initialStep={currentStep}
                      completedSteps={completedSteps}
                      onStepChange={handleStepChange}
                      className="h-full"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky h-full">
              <AskQuestionsSidebar
                contentId={contentId}
                contentTitle={title}
                onSendMessage={handleSendMessage}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Note Dialog */}
      {showNoteDialog && (
        <div className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 transition-all duration-300">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-lg mx-4 border-2 border-gray-200 dark:border-gray-600 shadow-2xl dark:shadow-black/40 transform transition-all duration-300 scale-100">
            <h3 className="text-xl font-bold mb-6 text-gray-900 dark:text-white bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent">
              {editingNoteIndex !== null ? "✏️ Edit Note" : "📝 Add Note"} for
              Step {currentStep + 1}
            </h3>
            <textarea
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder="Enter your note here... Share your thoughts, key insights, or questions about this step."
              className="w-full h-36 p-4 border-2 border-gray-300 dark:border-gray-600 rounded-xl resize-none focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 font-medium"
            />
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowNoteDialog(false);
                  setNoteContent("");
                  setEditingNoteIndex(null);
                }}
                className="px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 font-medium border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveNote}
                disabled={!noteContent.trim()}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg transition-all duration-200 disabled:cursor-not-allowed font-medium shadow-lg hover:shadow-xl border-2 border-purple-500 dark:border-purple-400 disabled:border-gray-400"
              >
                {editingNoteIndex !== null ? "💾 Update" : "💾 Save"} Note
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
