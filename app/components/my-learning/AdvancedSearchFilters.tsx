import { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown, 
  Calendar,
  Clock,
  Tag,
  BookOpen,
  CheckCircle
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '~/components/ui/collapsible';
import { Checkbox } from '~/components/ui/checkbox';
import { Slider } from '~/components/ui/slider';
import type { MyLearningFilters } from '~/db/services/learning-content';

interface AdvancedSearchFiltersProps {
  filters: MyLearningFilters;
  onFiltersChange: (filters: MyLearningFilters) => void;
  className?: string;
}

export function AdvancedSearchFilters({
  filters,
  onFiltersChange,
  className = '',
}: AdvancedSearchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState(filters.search || '');
  const [readingTimeRange, setReadingTimeRange] = useState([
    filters.readingTimeRange?.min || 5,
    filters.readingTimeRange?.max || 120
  ]);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== filters.search) {
        onFiltersChange({ ...filters, search: searchValue || undefined });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue, filters, onFiltersChange]);

  const handleFilterChange = (key: keyof MyLearningFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const clearAllFilters = () => {
    setSearchValue('');
    setReadingTimeRange([5, 120]);
    onFiltersChange({
      limit: filters.limit,
      offset: filters.offset,
    });
  };

  const hasActiveFilters = () => {
    return !!(
      filters.search ||
      filters.learningLevel ||
      filters.contentType ||
      filters.completionStatus ||
      filters.isPublic !== undefined ||
      filters.tags?.length ||
      filters.dateRange ||
      filters.readingTimeRange
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.learningLevel) count++;
    if (filters.contentType) count++;
    if (filters.completionStatus) count++;
    if (filters.isPublic !== undefined) count++;
    if (filters.tags?.length) count++;
    if (filters.dateRange) count++;
    if (filters.readingTimeRange) count++;
    return count;
  };

  return (
    <div className={`bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      {/* Search Bar */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search learning content..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="pl-10 pr-4"
          />
        </div>
      </div>

      {/* Filter Toggle */}
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-4 h-auto font-normal"
          >
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span>Advanced Filters</span>
              {hasActiveFilters() && (
                <Badge variant="secondary" className="ml-2">
                  {getActiveFilterCount()}
                </Badge>
              )}
            </div>
            <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="border-t border-gray-200 dark:border-gray-700">
          <div className="p-4 space-y-6">
            {/* Learning Level */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Learning Level
              </Label>
              <Select
                value={filters.learningLevel || 'all-levels'}
                onValueChange={(value) => 
                  handleFilterChange('learningLevel', value === 'all-levels' ? undefined : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-levels">All levels</SelectItem>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Content Type */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Content Type
              </Label>
              <Select
                value={filters.contentType || 'all-types'}
                onValueChange={(value) => 
                  handleFilterChange('contentType', value === 'all-types' ? undefined : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-types">All types</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="kwaci-primer">KWACI Primer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Completion Status */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Completion Status
              </Label>
              <Select
                value={filters.completionStatus || 'all-statuses'}
                onValueChange={(value) => 
                  handleFilterChange('completionStatus', value === 'all-statuses' ? undefined : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-statuses">All statuses</SelectItem>
                  <SelectItem value="not-started">Not Started</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Visibility */}
            <div className="space-y-3">
              <Label>Visibility</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="public"
                    checked={filters.isPublic === true}
                    onCheckedChange={(checked) => 
                      handleFilterChange('isPublic', checked ? true : undefined)
                    }
                  />
                  <Label htmlFor="public" className="text-sm">Public content only</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="private"
                    checked={filters.isPublic === false}
                    onCheckedChange={(checked) => 
                      handleFilterChange('isPublic', checked ? false : undefined)
                    }
                  />
                  <Label htmlFor="private" className="text-sm">Private content only</Label>
                </div>
              </div>
            </div>

            {/* Reading Time Range */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Reading Time (minutes)
              </Label>
              <div className="px-2">
                <Slider
                  value={readingTimeRange}
                  onValueChange={setReadingTimeRange}
                  onValueCommit={(value) => 
                    handleFilterChange('readingTimeRange', {
                      min: value[0],
                      max: value[1]
                    })
                  }
                  max={120}
                  min={5}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>{readingTimeRange[0]}m</span>
                  <span>{readingTimeRange[1]}m</span>
                </div>
              </div>
            </div>

            {/* Sort Options */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Sort By</Label>
                <Select
                  value={filters.sortBy || 'updatedAt'}
                  onValueChange={(value) => 
                    handleFilterChange('sortBy', value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="updatedAt">Last Updated</SelectItem>
                    <SelectItem value="createdAt">Date Created</SelectItem>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="progress">Progress</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Order</Label>
                <Select
                  value={filters.sortOrder || 'desc'}
                  onValueChange={(value) => 
                    handleFilterChange('sortOrder', value as 'asc' | 'desc')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Descending</SelectItem>
                    <SelectItem value="asc">Ascending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters() && (
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  variant="outline"
                  onClick={clearAllFilters}
                  className="w-full"
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear All Filters
                </Button>
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
