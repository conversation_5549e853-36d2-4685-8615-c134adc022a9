import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  Check, 
  Share2, 
  Eye, 
  EyeOff, 
  ExternalLink,
  Twitter,
  Facebook,
  Linkedin,
  Mail
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import { Switch } from '~/components/ui/switch';
import { Separator } from '~/components/ui/separator';
import type { LearningContentWithProgress } from '~/db/services/learning-content';

interface ShareContentModalProps {
  content: LearningContentWithProgress | null;
  isOpen: boolean;
  onClose: () => void;
  onTogglePublic?: (id: string) => void;
}

export function ShareContentModal({
  content,
  isOpen,
  onClose,
  onTogglePublic,
}: ShareContentModalProps) {
  const [copied, setCopied] = useState(false);

  if (!content) return null;

  const shareUrl = `${window.location.origin}/dashboard/learn/${content.id}`;
  const publicUrl = content.isPublic ? shareUrl : null;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const shareToSocial = (platform: string) => {
    const title = encodeURIComponent(content.title);
    const url = encodeURIComponent(publicUrl || shareUrl);
    const description = encodeURIComponent(content.description);

    let shareUrl = '';
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${title}&url=${url}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${title}&body=${description}%0A%0A${url}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  const handleTogglePublic = () => {
    onTogglePublic?.(content.id);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Learning Content
          </DialogTitle>
          <DialogDescription>
            Share this learning content with others or manage its visibility.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Content Preview */}
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h3 className="font-semibold text-sm mb-1">{content.title}</h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
              {content.description}
            </p>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline" className="text-xs">
                {content.learningLevel}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {content.contentType === 'kwaci-primer' ? 'KWACI Primer' : 'Standard'}
              </Badge>
            </div>
          </div>

          {/* Visibility Toggle */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {content.isPublic ? (
                  <Eye className="h-4 w-4 text-green-600" />
                ) : (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                )}
                <Label htmlFor="public-toggle" className="text-sm font-medium">
                  Make content public
                </Label>
              </div>
              <Switch
                id="public-toggle"
                checked={content.isPublic}
                onCheckedChange={handleTogglePublic}
              />
            </div>
            <p className="text-xs text-gray-500">
              {content.isPublic 
                ? "Anyone with the link can view this content"
                : "Only you can view this content"
              }
            </p>
          </div>

          <Separator />

          {/* Share Link */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Share Link</Label>
            <div className="flex gap-2">
              <Input
                value={publicUrl || shareUrl}
                readOnly
                className="flex-1"
              />
              <Button
                size="sm"
                onClick={() => copyToClipboard(publicUrl || shareUrl)}
                className="flex-shrink-0"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
            {!content.isPublic && (
              <p className="text-xs text-amber-600 dark:text-amber-400">
                ⚠️ This content is private. Others won't be able to view it even with the link.
              </p>
            )}
          </div>

          {/* Social Sharing */}
          {content.isPublic && (
            <>
              <Separator />
              <div className="space-y-3">
                <Label className="text-sm font-medium">Share on Social Media</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareToSocial('twitter')}
                    className="flex items-center gap-2"
                  >
                    <Twitter className="h-4 w-4" />
                    Twitter
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareToSocial('facebook')}
                    className="flex items-center gap-2"
                  >
                    <Facebook className="h-4 w-4" />
                    Facebook
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareToSocial('linkedin')}
                    className="flex items-center gap-2"
                  >
                    <Linkedin className="h-4 w-4" />
                    LinkedIn
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareToSocial('email')}
                    className="flex items-center gap-2"
                  >
                    <Mail className="h-4 w-4" />
                    Email
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* View Content Link */}
          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={() => {
                window.open(`/dashboard/learn/${content.id}`, '_blank');
              }}
              className="w-full flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              View Content
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
